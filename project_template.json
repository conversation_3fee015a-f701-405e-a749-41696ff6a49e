{"template_info": {"name": "C++ Data Management System Template", "version": "1.0.0", "description": "قالب شامل لإنشاء أنظمة إدارة البيانات مع دعم Excel", "author": "AI Assistant", "language": "C++17", "created_date": "2025-01-25", "license": "MIT"}, "project_structure": {"required_files": [{"name": "main_program.cpp", "description": "الملف الرئيسي للبرنامج", "template_variables": ["SYSTEM_NAME", "ENTITY_NAME", "ENTITIES_NAME"]}, {"name": "xlsxwriter.h", "description": "ملف رأس مكتبة Excel", "template_variables": []}, {"name": "xlsxwriter.cpp", "description": "تنفيذ مكتبة Excel", "template_variables": []}, {"name": "test_program.cpp", "description": "برنامج اختبار المكتبة", "template_variables": ["SYSTEM_NAME"]}, {"name": "build.bat", "description": "سكريبت بناء Windows", "template_variables": ["PROGRAM_NAME"]}, {"name": "build.sh", "description": "سكريبت بناء Unix/Linux", "template_variables": ["PROGRAM_NAME"]}, {"name": "<PERSON><PERSON><PERSON>", "description": "نظام بناء متقدم", "template_variables": ["PROGRAM_NAME"]}, {"name": "README.md", "description": "دليل المستخدم", "template_variables": ["SYSTEM_NAME", "SYSTEM_DESCRIPTION"]}, {"name": "config.json", "description": "مل<PERSON> التكوين", "template_variables": ["SYSTEM_NAME", "ENTITY_NAME", "ENTITIES_NAME"]}], "optional_directories": [{"name": "src", "description": "ملفات المصدر"}, {"name": "include", "description": "ملفات الرأس"}, {"name": "build", "description": "ملفات البناء"}, {"name": "data", "description": "ملفات البيانات"}, {"name": "tests", "description": "ملفات الاختبار"}, {"name": "docs", "description": "التوثيق"}]}, "template_variables": {"system_variables": {"SYSTEM_NAME": {"description": "اسم النظام", "type": "string", "example": "Student Management", "required": true}, "SYSTEM_DESCRIPTION": {"description": "وصف النظام", "type": "string", "example": "نظام شامل لإدارة بيانات الطلاب مع دعم Excel", "required": true}, "PROGRAM_NAME": {"description": "اسم البرنامج التنفيذي", "type": "string", "example": "student_manager", "required": true}, "AUTHOR_NAME": {"description": "اسم المطور", "type": "string", "example": "<PERSON><PERSON><PERSON><PERSON> أحمد", "required": false}}, "entity_variables": {"ENTITY_NAME": {"description": "اسم الكيان (مف<PERSON><PERSON>)", "type": "string", "example": "Student", "required": true}, "ENTITIES_NAME": {"description": "اسم الكيان (جمع)", "type": "string", "example": "Students", "required": true}}, "field_variables": {"FIELD1_NAME": {"description": "اسم الحقل الأول", "type": "string", "example": "name", "required": true}, "FIELD1_TYPE": {"description": "نوع الحقل الأول", "type": "string", "options": ["string", "int", "double", "bool"], "example": "string", "required": true}, "FIELD2_NAME": {"description": "اسم الحقل الثاني", "type": "string", "example": "age", "required": true}, "FIELD2_TYPE": {"description": "نوع الحقل الثاني", "type": "string", "options": ["string", "int", "double", "bool"], "example": "int", "required": true}, "FIELD3_NAME": {"description": "اسم الحقل الثالث", "type": "string", "example": "department", "required": false}, "FIELD3_TYPE": {"description": "نوع الحقل الثالث", "type": "string", "options": ["string", "int", "double", "bool"], "example": "string", "required": false}}}, "data_structure_template": {"struct_definition": "struct [ENTITY_NAME] {\n    int id;\n    [FIELD_DEFINITIONS]\n};", "field_definition_patterns": {"string": "string [FIELD_NAME];", "int": "int [FIELD_NAME];", "double": "double [FIELD_NAME];", "bool": "bool [FIELD_NAME];"}}, "function_templates": {"core_functions": [{"name": "add_[entity]", "description": "إضافة عنصر جديد", "parameters": [], "return_type": "void"}, {"name": "view_all_[entities]", "description": "عرض جميع العناصر", "parameters": [], "return_type": "void"}, {"name": "search_[entity]", "description": "البحث عن عنصر", "parameters": ["int id"], "return_type": "[ENTITY_NAME]*"}, {"name": "update_[entity]", "description": "تحديث عنصر", "parameters": ["int id"], "return_type": "bool"}, {"name": "delete_[entity]", "description": "<PERSON><PERSON><PERSON> عنصر", "parameters": ["int id"], "return_type": "bool"}, {"name": "save_to_excel", "description": "حفظ البيانات في Excel", "parameters": [], "return_type": "bool"}, {"name": "load_from_excel", "description": "تحميل البيانات من Excel", "parameters": [], "return_type": "bool"}], "utility_functions": [{"name": "validate_input", "description": "التحقق من صحة الإدخال", "parameters": ["string input", "string type"], "return_type": "bool"}, {"name": "clear_screen", "description": "تنظيف الشاشة", "parameters": [], "return_type": "void"}, {"name": "pause_system", "description": "انتظار المستخدم", "parameters": [], "return_type": "void"}, {"name": "show_menu", "description": "عرض القائمة الرئيسية", "parameters": [], "return_type": "void"}, {"name": "get_user_choice", "description": "الحصول على اختيار المستخدم", "parameters": [], "return_type": "int"}], "file_functions": [{"name": "create_test_files", "description": "إنشاء ملفات اختبار", "parameters": ["int count"], "return_type": "bool"}, {"name": "delete_all_files", "description": "حذ<PERSON> جميع الملفات", "parameters": [], "return_type": "bool"}, {"name": "backup_data", "description": "إنشاء نسخة احتياطية", "parameters": [], "return_type": "bool"}, {"name": "restore_data", "description": "استرداد البيانات", "parameters": ["string backup_file"], "return_type": "bool"}]}, "ui_templates": {"main_menu": {"header": "========================================\n    [SYSTEM_NAME] Management System\n========================================", "options": ["1. Add new [entity]", "2. View all [entities]", "3. Search [entity]", "4. Update [entity]", "5. Delete [entity]", "6. Create test files", "7. Delete all files", "8. Export data", "9. Import data", "10. Save and exit"], "footer": "----------------------------------------\nEnter your choice (1-10):"}, "input_form": {"header": "=== Add New [Entity] ===", "fields": ["Enter [field1]: ", "Enter [field2]: ", "Enter [field3]: "], "success_message": "[Entity] added successfully! (ID: [id])"}, "display_format": {"header": "=== All [Entities] ===", "table_header": "ID   [Field1]             [Field2]  [Field3]\n-------------------------------------------------------", "row_format": "%-4d %-20s %-8s %-20s", "footer": "\nTotal [entities]: [count]"}}, "build_system": {"compiler_options": {"standard": "c++17", "warnings": ["-Wall", "-Wextra"], "optimization": "-O2", "include_paths": ["-I."]}, "build_targets": {"main": {"sources": ["main_program.cpp", "xlsxwriter.cpp"], "output": "[PROGRAM_NAME]"}, "test": {"sources": ["test_program.cpp", "xlsxwriter.cpp"], "output": "test_[PROGRAM_NAME]"}}}, "validation_rules": {"field_validation": {"string": {"min_length": 1, "max_length": 50, "allow_empty": false}, "int": {"min_value": 1, "max_value": 999999}, "double": {"min_value": 0.0, "max_value": 999999.99, "decimal_places": 2}}}, "example_projects": [{"name": "Employee Management System", "variables": {"SYSTEM_NAME": "Employee Management", "ENTITY_NAME": "Employee", "ENTITIES_NAME": "Employees", "FIELD1_NAME": "name", "FIELD1_TYPE": "string", "FIELD2_NAME": "salary", "FIELD2_TYPE": "int", "FIELD3_NAME": "department", "FIELD3_TYPE": "string", "PROGRAM_NAME": "employee_manager"}}, {"name": "Product Inventory System", "variables": {"SYSTEM_NAME": "Product Inventory", "ENTITY_NAME": "Product", "ENTITIES_NAME": "Products", "FIELD1_NAME": "name", "FIELD1_TYPE": "string", "FIELD2_NAME": "price", "FIELD2_TYPE": "double", "FIELD3_NAME": "category", "FIELD3_TYPE": "string", "PROGRAM_NAME": "inventory_manager"}}, {"name": "Library Management System", "variables": {"SYSTEM_NAME": "Library Management", "ENTITY_NAME": "Book", "ENTITIES_NAME": "Books", "FIELD1_NAME": "title", "FIELD1_TYPE": "string", "FIELD2_NAME": "pages", "FIELD2_TYPE": "int", "FIELD3_NAME": "author", "FIELD3_TYPE": "string", "PROGRAM_NAME": "library_manager"}}], "usage_instructions": {"steps": ["1. اختر نوع النظام المراد إنشاؤه", "2. <PERSON><PERSON><PERSON> المتغيرات المطلوبة من template_variables", "3. استبدل جميع المتغيرات في ملفات القالب", "4. قم بتخصيص هيكل البيانات حسب احتياجاتك", "5. اخت<PERSON><PERSON> البرنامج وتأكد من عمله بشكل صحيح", "6. أض<PERSON> أي وظائف إضافية مطلوبة", "7. أنشئ التوثيق والدليل النهائي"], "tips": ["استخدم أسماء واضحة ومفهومة للمتغيرات", "تأكد من اختبار جميع الوظائف قبل النشر", "أض<PERSON> التحق<PERSON> من صحة البيانات لجميع الحقول", "استخدم معالجة الأخطاء المناسبة", "وثق الكود بشكل جيد للصيانة المستقبلية"]}}