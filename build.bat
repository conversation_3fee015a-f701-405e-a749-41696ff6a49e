@echo off
echo ========================================
echo   Student Management System - Build
echo ========================================
echo.

echo Checking for C++ compiler...
g++ --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: g++ compiler not found!
    echo Please install MinGW-w64 or another C++ compiler.
    echo You can download it from: https://www.mingw-w64.org/
    pause
    exit /b 1
)

echo Compiler found. Building project...
echo.

echo Compiling student_manager.cpp...
g++ -std=c++17 -Wall -Wextra -O2 -o student_manager.exe student_manager.cpp xlsxwriter.cpp -I.

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo   Build Successful!
    echo ========================================
    echo.
    echo Executable created: student_manager.exe
    echo.
    echo To run the program, type:
    echo   student_manager.exe
    echo.
    echo Or simply double-click on student_manager.exe
    echo.
) else (
    echo.
    echo ========================================
    echo   Build Failed!
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo Make sure all source files are present:
    echo   - student_manager.cpp
    echo   - xlsxwriter.cpp
    echo   - xlsxwriter.h
    echo.
)

pause
