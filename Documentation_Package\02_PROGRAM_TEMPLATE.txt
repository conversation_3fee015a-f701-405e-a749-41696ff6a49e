===============================================================================
                        قالب برنامج إدارة البيانات - C++
                      Data Management Program Template
===============================================================================

🎯 الغرض من هذا القالب:
هذا القالب يوفر هيكلاً أساسياً لإنشاء برامج إدارة البيانات باستخدام C++17
مع دعم Excel والإحصائيات المتقدمة. يمكن تخصيصه لأي نوع من البيانات.

===============================================================================
📋 هيكل القالب الأساسي:
===============================================================================

🔧 المكتبات المطلوبة:
```cpp
#include <iostream>        // إدخال وإخراج
#include <fstream>         // ملفات
#include <string>          // نصوص
#include <vector>          // مصفوفات ديناميكية
#include <filesystem>      // إدارة الملفات
#include <sstream>         // معالجة النصوص
#include <limits>          // حدود البيانات
#include <iomanip>         // تنسيق الإخراج
#include <random>          // أرقام عشوائية
#include <algorithm>       // خوارزميات
#include <map>             // خرائط البيانات
#include "xlsxwriter.h"    // مكتبة Excel مخصصة
```

===============================================================================
📊 هيكل البيانات الأساسي:
===============================================================================

🎯 قالب هيكل البيانات:
```cpp
struct [ENTITY_NAME] {
    int id;                           // معرف فريد
    std::string [FIELD_1];           // حقل نصي أساسي
    int [FIELD_2];                   // حقل رقمي
    std::string [FIELD_3];           // حقل تصنيف
    
    // منشئات
    [ENTITY_NAME]();
    [ENTITY_NAME](int id, const std::string& field1, 
                  int field2, const std::string& field3);
};

// متغيرات عامة
std::vector<[ENTITY_NAME]> [entities];
int next_[entity]_id = 1;
```

===============================================================================
🎲 نظام البيانات العشوائية:
===============================================================================

📝 قوالب البيانات العشوائية:
```cpp
// بيانات عشوائية للحقل الأول
std::vector<std::string> [field1]_options = {
    "[OPTION_1]", "[OPTION_2]", "[OPTION_3]", "[OPTION_4]",
    "[OPTION_5]", "[OPTION_6]", "[OPTION_7]", "[OPTION_8]",
    // ... أضف المزيد حسب الحاجة (20-50 خيار)
};

// بيانات عشوائية للحقل الثالث (التصنيفات)
std::vector<std::string> [field3]_categories = {
    "[CATEGORY_1]", "[CATEGORY_2]", "[CATEGORY_3]", "[CATEGORY_4]",
    "[CATEGORY_5]", "[CATEGORY_6]", "[CATEGORY_7]", "[CATEGORY_8]",
    // ... أضف المزيد حسب الحاجة (10-20 تصنيف)
};

// مولد الأرقام العشوائية
std::random_device rd;
std::mt19937 gen(rd());
```

===============================================================================
⚙️ الوظائف الأساسية:
===============================================================================

🏗️ وظائف إدارة Excel:
```cpp
lxw_workbook* init_excel_file(const std::string& filename) {
    lxw_workbook* workbook = workbook_new(filename.c_str());
    if (!workbook) return nullptr;
    
    lxw_worksheet* worksheet = workbook_add_worksheet(workbook, "[SHEET_NAME]");
    
    // تنسيق الرؤوس
    lxw_format* header_format = workbook_add_format(workbook);
    format_set_bold(header_format);
    format_set_align(header_format, LXW_ALIGN_CENTER);
    format_set_bg_color(header_format, LXW_COLOR_GRAY);
    
    // كتابة الرؤوس
    worksheet_write_string(worksheet, 0, 0, "ID", header_format);
    worksheet_write_string(worksheet, 0, 1, "[FIELD_1_HEADER]", header_format);
    worksheet_write_string(worksheet, 0, 2, "[FIELD_2_HEADER]", header_format);
    worksheet_write_string(worksheet, 0, 3, "[FIELD_3_HEADER]", header_format);
    
    return workbook;
}

void save_[entities]_to_excel(const std::string& filename) {
    lxw_workbook* workbook = init_excel_file(filename);
    if (!workbook) {
        std::cout << "Error: Could not create Excel file: " << filename << std::endl;
        return;
    }
    
    lxw_worksheet* worksheet = workbook_get_worksheet_by_name(workbook, "[SHEET_NAME]");
    
    // كتابة البيانات
    for (size_t i = 0; i < [entities].size(); ++i) {
        worksheet_write_number(worksheet, i + 1, 0, [entities][i].id, nullptr);
        worksheet_write_string(worksheet, i + 1, 1, [entities][i].[field_1].c_str(), nullptr);
        worksheet_write_number(worksheet, i + 1, 2, [entities][i].[field_2], nullptr);
        worksheet_write_string(worksheet, i + 1, 3, [entities][i].[field_3].c_str(), nullptr);
    }
    
    workbook_close(workbook);
}
```

===============================================================================
👤 وظائف إدارة البيانات:
===============================================================================

```cpp
void add_[entity]() {
    std::cout << "\n=== Add New [ENTITY_NAME] ===\n";
    
    std::string [field_1];
    int [field_2];
    std::string [field_3];
    
    // إدخال البيانات مع التحقق
    std::cout << "Enter [field_1]: ";
    std::cin.ignore();
    std::getline(std::cin, [field_1]);
    
    std::cout << "Enter [field_2] ([MIN_VALUE]-[MAX_VALUE]): ";
    while (!(std::cin >> [field_2]) || [field_2] < [MIN_VALUE] || [field_2] > [MAX_VALUE]) {
        std::cout << "Invalid input. Please enter a number between [MIN_VALUE] and [MAX_VALUE]: ";
        std::cin.clear();
        std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    }
    
    std::cout << "Enter [field_3]: ";
    std::cin.ignore();
    std::getline(std::cin, [field_3]);
    
    // إضافة البيانات
    [entities].emplace_back(next_[entity]_id++, [field_1], [field_2], [field_3]);
    std::cout << "[ENTITY_NAME] added successfully!\n";
}

void view_all_[entities]() {
    if ([entities].empty()) {
        std::cout << "\nNo [entities] found.\n";
        return;
    }
    
    std::cout << "\n=== All [ENTITIES] ===\n";
    std::cout << std::left << std::setw(5) << "ID" 
              << std::setw(20) << "[FIELD_1_HEADER]"
              << std::setw(10) << "[FIELD_2_HEADER]"
              << std::setw(25) << "[FIELD_3_HEADER]" << std::endl;
    std::cout << std::string(60, '-') << std::endl;
    
    for (const auto& [entity] : [entities]) {
        std::cout << std::left << std::setw(5) << [entity].id
                  << std::setw(20) << [entity].[field_1]
                  << std::setw(10) << [entity].[field_2]
                  << std::setw(25) << [entity].[field_3] << std::endl;
    }
    
    std::cout << "\nTotal [entities]: " << [entities].size() << std::endl;
}
```

===============================================================================
🎲 وظائف البيانات العشوائية:
===============================================================================

```cpp
std::string generate_random_[field_1]() {
    std::uniform_int_distribution<> dist(0, [field1]_options.size() - 1);
    return [field1]_options[dist(gen)];
}

int generate_random_[field_2]() {
    std::uniform_int_distribution<> dist([MIN_VALUE], [MAX_VALUE]);
    return dist(gen);
}

std::string generate_random_[field_3]() {
    std::uniform_int_distribution<> dist(0, [field3]_categories.size() - 1);
    return [field3]_categories[dist(gen)];
}

void create_test_files() {
    std::cout << "\n=== Create Test Files with Random Data ===\n";
    
    int num_files;
    std::cout << "Enter number of files to create (1-1000): ";
    while (!(std::cin >> num_files) || num_files < 1 || num_files > 1000) {
        std::cout << "Invalid input. Please enter a number between 1 and 1000: ";
        std::cin.clear();
        std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
    }
    
    std::cout << "\nCreating " << num_files << " test files with random [entity] data...\n";
    std::cout << "This may take a moment...\n";
    
    int total_[entities]_created = 0;
    
    for (int i = 1; i <= num_files; ++i) {
        std::string filename = "[entities]_" + std::to_string(i) + ".xlsx";
        
        // توليد عدد عشوائي من البيانات لكل ملف
        std::uniform_int_distribution<> count_dist([MIN_ENTITIES_PER_FILE], [MAX_ENTITIES_PER_FILE]);
        int [entities]_count = count_dist(gen);
        
        // إنشاء بيانات عشوائية مؤقتة
        std::vector<[ENTITY_NAME]> temp_[entities];
        for (int j = 0; j < [entities]_count; ++j) {
            temp_[entities].emplace_back(
                j + 1,
                generate_random_[field_1](),
                generate_random_[field_2](),
                generate_random_[field_3]()
            );
        }
        
        // حفظ في ملف Excel
        // [كود حفظ البيانات المؤقتة]
        
        total_[entities]_created += [entities]_count;
        
        // عرض التقدم
        if (i % (num_files / 10 + 1) == 0 || i == num_files) {
            int progress = (i * 100) / num_files;
            std::cout << "Created " << i << " files... (" << progress << "%)\n";
        }
    }
    
    std::cout << "\nTest file creation completed!\n";
    std::cout << "Successfully created: " << num_files << " files\n";
    std::cout << "Estimated total [entities] created: ~" << total_[entities]_created << " [entities]\n";
}
```

===============================================================================
📊 وظائف الإحصائيات:
===============================================================================

```cpp
void show_statistics() {
    if ([entities].empty()) {
        std::cout << "\n=== Statistics ===\n";
        std::cout << "No [entity] data loaded. Please load [entity] files first.\n";
        return;
    }
    
    std::cout << "\n=== [ENTITY_NAME] Statistics ===\n";
    std::cout << "Total [ENTITIES]: " << [entities].size() << "\n\n";
    
    // إحصائيات الحقل الرقمي
    if (![entities].empty()) {
        double sum = 0;
        int min_val = [entities][0].[field_2];
        int max_val = [entities][0].[field_2];
        std::map<int, int> [field_2]_distribution;
        
        for (const auto& [entity] : [entities]) {
            sum += [entity].[field_2];
            min_val = std::min(min_val, [entity].[field_2]);
            max_val = std::max(max_val, [entity].[field_2]);
            [field_2]_distribution[[entity].[field_2]]++;
        }
        
        double average = sum / [entities].size();
        
        std::cout << "[FIELD_2_HEADER] Statistics:\n";
        std::cout << "  Average [FIELD_2_HEADER]: " << std::fixed << std::setprecision(1) << average << "\n";
        std::cout << "  Minimum [FIELD_2_HEADER]: " << min_val << "\n";
        std::cout << "  Maximum [FIELD_2_HEADER]: " << max_val << "\n\n";
        
        // توزيع القيم
        std::cout << "[FIELD_2_HEADER] Distribution:\n";
        for (const auto& pair : [field_2]_distribution) {
            std::cout << "[FIELD_2_HEADER] " << pair.first << ": " << pair.second << " [entities]\n";
        }
        std::cout << "\n";
    }
    
    // إحصائيات التصنيفات
    std::map<std::string, int> [field_3]_count;
    for (const auto& [entity] : [entities]) {
        [field_3]_count[[entity].[field_3]]++;
    }
    
    std::cout << "[FIELD_3_HEADER] Distribution:\n";
    std::cout << std::left << std::setw(25) << "[FIELD_3_HEADER]" 
              << std::setw(10) << "Count" 
              << std::setw(15) << "Percentage" << std::endl;
    std::cout << std::string(50, '-') << std::endl;
    
    for (const auto& pair : [field_3]_count) {
        double percentage = (double)pair.second / [entities].size() * 100;
        std::cout << std::left << std::setw(25) << pair.first
                  << std::setw(10) << pair.second
                  << std::fixed << std::setprecision(1) << percentage << "%" << std::endl;
    }
    
    // أكثر التصنيفات شعبية
    std::vector<std::pair<std::string, int>> sorted_[field_3](
        [field_3]_count.begin(), [field_3]_count.end()
    );
    std::sort(sorted_[field_3].begin(), sorted_[field_3].end(),
              [](const auto& a, const auto& b) { return a.second > b.second; });
    
    std::cout << "\nTop 5 Most Popular [FIELD_3_HEADER]:\n";
    for (size_t i = 0; i < std::min(5ul, sorted_[field_3].size()); ++i) {
        std::cout << (i + 1) << ". " << sorted_[field_3][i].first 
                  << " (" << sorted_[field_3][i].second << " [entities])\n";
    }
}
```

===============================================================================
🎮 واجهة المستخدم:
===============================================================================

```cpp
void show_main_menu() {
    std::cout << "\n==================================================\n";
    std::cout << "    [SYSTEM_NAME] ([DATA_TYPE])\n";
    std::cout << "==================================================\n";
    std::cout << "1. Add new [entity]\n";
    std::cout << "2. View all [entities]\n";
    std::cout << "3. Create test files with random data\n";
    std::cout << "4. Load all [entity] files\n";
    std::cout << "5. Show statistics\n";
    std::cout << "6. Delete all [entity] files\n";
    std::cout << "7. Save and exit\n";
    std::cout << "--------------------------------------------------\n";
    std::cout << "Enter your choice (1-7): ";
}

int main() {
    std::cout << "Welcome to [SYSTEM_NAME]!\n";
    
    int choice;
    while (true) {
        show_main_menu();
        
        if (!(std::cin >> choice)) {
            std::cout << "Invalid input. Please enter a number.\n";
            std::cin.clear();
            std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
            continue;
        }
        
        switch (choice) {
            case 1:
                add_[entity]();
                break;
            case 2:
                view_all_[entities]();
                break;
            case 3:
                create_test_files();
                break;
            case 4:
                load_all_[entity]_files();
                break;
            case 5:
                show_statistics();
                break;
            case 6:
                delete_all_files();
                break;
            case 7:
                save_[entities]_to_excel("[entities]_final.xlsx");
                std::cout << "Data saved. Goodbye!\n";
                return 0;
            default:
                std::cout << "Invalid choice. Please try again.\n";
        }
    }
    
    return 0;
}
```

===============================================================================
🔧 تعليمات التخصيص:
===============================================================================

📝 الخطوات المطلوبة للتخصيص:

1. **استبدال المتغيرات الأساسية:**
   - [ENTITY_NAME] → اسم الكيان (مثل: Student, Employee, Product)
   - [entities] → الجمع بأحرف صغيرة (مثل: students, employees, products)
   - [ENTITIES] → الجمع بأحرف كبيرة (مثل: STUDENTS, EMPLOYEES, PRODUCTS)
   - [entity] → المفرد بأحرف صغيرة (مثل: student, employee, product)

2. **تخصيص الحقول:**
   - [FIELD_1] → الحقل النصي الأول (مثل: name, title, description)
   - [FIELD_2] → الحقل الرقمي (مثل: age, salary, price)
   - [FIELD_3] → حقل التصنيف (مثل: department, category, type)

3. **تخصيص الرؤوس:**
   - [FIELD_1_HEADER] → عنوان العمود الأول
   - [FIELD_2_HEADER] → عنوان العمود الرقمي
   - [FIELD_3_HEADER] → عنوان عمود التصنيف

4. **تخصيص القيم:**
   - [MIN_VALUE], [MAX_VALUE] → نطاق القيم الرقمية
   - [MIN_ENTITIES_PER_FILE], [MAX_ENTITIES_PER_FILE] → عدد السجلات لكل ملف

5. **تخصيص البيانات العشوائية:**
   - [field1]_options → قائمة الخيارات للحقل الأول
   - [field3]_categories → قائمة التصنيفات

6. **تخصيص النظام:**
   - [SYSTEM_NAME] → اسم النظام
   - [DATA_TYPE] → نوع البيانات
   - [SHEET_NAME] → اسم ورقة Excel

===============================================================================
🎯 أمثلة للتخصيص:
===============================================================================

📚 مثال 1: نظام إدارة المكتبة
- ENTITY_NAME: Book
- FIELD_1: title (العنوان)
- FIELD_2: pages (عدد الصفحات)
- FIELD_3: category (التصنيف)

🏢 مثال 2: نظام إدارة الموظفين
- ENTITY_NAME: Employee
- FIELD_1: name (الاسم)
- FIELD_2: salary (الراتب)
- FIELD_3: department (القسم)

🛒 مثال 3: نظام إدارة المخزون
- ENTITY_NAME: Product
- FIELD_1: name (اسم المنتج)
- FIELD_2: price (السعر)
- FIELD_3: category (الفئة)

===============================================================================
📋 قائمة التحقق للتخصيص:
===============================================================================

✅ قبل البدء:
□ تحديد نوع البيانات المطلوب إدارتها
□ تحديد الحقول الأساسية (3-5 حقول)
□ إعداد قوائم البيانات العشوائية
□ تحديد نطاقات القيم الرقمية

✅ أثناء التخصيص:
□ استبدال جميع المتغيرات النموذجية
□ تحديث أسماء الوظائف
□ تخصيص رسائل واجهة المستخدم
□ تحديث تنسيق Excel

✅ بعد التخصيص:
□ اختبار التجميع
□ اختبار جميع الوظائف
□ التحقق من البيانات العشوائية
□ اختبار الإحصائيات

===============================================================================
🚀 نصائح للتطوير:
===============================================================================

💡 أفضل الممارسات:
- استخدم أسماء واضحة ومعبرة
- أضف تعليقات باللغة المناسبة
- اتبع معايير C++ الحديثة
- اختبر كل وظيفة بشكل منفصل
- استخدم معالجة الأخطاء الشاملة

🔧 تحسينات مقترحة:
- إضافة وظائف البحث والفلترة
- دعم قواعد البيانات الحقيقية
- واجهة رسومية GUI
- تصدير تقارير متقدمة
- نظام النسخ الاحتياطية

===============================================================================
📝 ملاحظات نهائية:
===============================================================================

هذا القالب يوفر أساساً قوياً لبناء أنظمة إدارة البيانات المختلفة.
يمكن توسيعه وتخصيصه حسب الحاجة مع الحفاظ على الهيكل الأساسي
والوظائف الأساسية.

تأكد من اختبار النظام بشكل شامل بعد التخصيص وقبل الاستخدام الفعلي.

===============================================================================
تاريخ الإنشاء: 2025-01-25
الإصدار: 1.0 Template
المطور: AI Assistant
===============================================================================
