#include <iostream>
#include <string>
#include "xlsxwriter.h"

int main() {
    std::cout << "Testing xlsxwriter implementation..." << std::endl;
    
    // Test 1: Create a simple workbook
    std::cout << "Test 1: Creating workbook..." << std::endl;
    lxw_workbook* wb = workbook_new("test.xlsx");
    if (!wb) {
        std::cerr << "ERROR: Failed to create workbook!" << std::endl;
        return 1;
    }
    std::cout << "✓ Workbook created successfully" << std::endl;
    
    // Test 2: Add a worksheet
    std::cout << "Test 2: Adding worksheet..." << std::endl;
    lxw_worksheet* ws = workbook_add_worksheet(wb, "TestSheet");
    if (!ws) {
        std::cerr << "ERROR: Failed to add worksheet!" << std::endl;
        workbook_close(wb);
        return 1;
    }
    std::cout << "✓ Worksheet added successfully" << std::endl;
    
    // Test 3: Create and test format
    std::cout << "Test 3: Creating format..." << std::endl;
    lxw_format* fmt = workbook_add_format(wb);
    if (!fmt) {
        std::cerr << "ERROR: Failed to create format!" << std::endl;
        workbook_close(wb);
        return 1;
    }
    format_set_bold(fmt);
    format_set_align(fmt, LXW_ALIGN_CENTER);
    format_set_bg_color(fmt, LXW_COLOR_GRAY);
    std::cout << "✓ Format created and configured successfully" << std::endl;
    
    // Test 4: Write data to worksheet
    std::cout << "Test 4: Writing data to worksheet..." << std::endl;
    
    // Write headers
    lxw_error result = worksheet_write_string(ws, 0, 0, "ID", fmt);
    if (result != LXW_NO_ERROR) {
        std::cerr << "ERROR: Failed to write header 'ID'!" << std::endl;
        workbook_close(wb);
        return 1;
    }
    
    result = worksheet_write_string(ws, 0, 1, "Name", fmt);
    if (result != LXW_NO_ERROR) {
        std::cerr << "ERROR: Failed to write header 'Name'!" << std::endl;
        workbook_close(wb);
        return 1;
    }
    
    result = worksheet_write_string(ws, 0, 2, "Age", fmt);
    if (result != LXW_NO_ERROR) {
        std::cerr << "ERROR: Failed to write header 'Age'!" << std::endl;
        workbook_close(wb);
        return 1;
    }
    
    result = worksheet_write_string(ws, 0, 3, "Department", fmt);
    if (result != LXW_NO_ERROR) {
        std::cerr << "ERROR: Failed to write header 'Department'!" << std::endl;
        workbook_close(wb);
        return 1;
    }
    
    // Write sample data
    result = worksheet_write_number(ws, 1, 0, 1, nullptr);
    if (result != LXW_NO_ERROR) {
        std::cerr << "ERROR: Failed to write number!" << std::endl;
        workbook_close(wb);
        return 1;
    }
    
    result = worksheet_write_string(ws, 1, 1, "John Doe", nullptr);
    if (result != LXW_NO_ERROR) {
        std::cerr << "ERROR: Failed to write string!" << std::endl;
        workbook_close(wb);
        return 1;
    }
    
    result = worksheet_write_number(ws, 1, 2, 25, nullptr);
    if (result != LXW_NO_ERROR) {
        std::cerr << "ERROR: Failed to write age!" << std::endl;
        workbook_close(wb);
        return 1;
    }
    
    result = worksheet_write_string(ws, 1, 3, "Computer Science", nullptr);
    if (result != LXW_NO_ERROR) {
        std::cerr << "ERROR: Failed to write department!" << std::endl;
        workbook_close(wb);
        return 1;
    }
    
    std::cout << "✓ Data written successfully" << std::endl;
    
    // Test 5: Get worksheet by name
    std::cout << "Test 5: Getting worksheet by name..." << std::endl;
    lxw_worksheet* ws_test = workbook_get_worksheet_by_name(wb, "TestSheet");
    if (!ws_test) {
        std::cerr << "ERROR: Failed to get worksheet by name!" << std::endl;
        workbook_close(wb);
        return 1;
    }
    std::cout << "✓ Worksheet retrieved successfully" << std::endl;
    
    // Test 6: Close workbook
    std::cout << "Test 6: Closing workbook..." << std::endl;
    result = workbook_close(wb);
    if (result != LXW_NO_ERROR) {
        std::cerr << "ERROR: Failed to close workbook!" << std::endl;
        return 1;
    }
    std::cout << "✓ Workbook closed successfully" << std::endl;
    
    std::cout << std::endl;
    std::cout << "========================================" << std::endl;
    std::cout << "All tests passed successfully!" << std::endl;
    std::cout << "xlsxwriter implementation is working." << std::endl;
    std::cout << "Output file: test.xlsx" << std::endl;
    std::cout << "========================================" << std::endl;
    
    return 0;
}
