#ifndef XLSXWRITER_H
#define XLSXWRITER_H

#include <stdio.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Color constants for formatting */
#define LXW_COLOR_BLACK     0x1000000
#define LXW_COLOR_BLUE      0x0000FF
#define LXW_COLOR_BROWN     0xA52A2A
#define LXW_COLOR_CYAN      0x00FFFF
#define LXW_COLOR_GRAY      0x808080
#define LXW_COLOR_GREEN     0x008000
#define LXW_COLOR_LIME      0x00FF00
#define LXW_COLOR_MAGENTA   0xFF00FF
#define LXW_COLOR_NAVY      0x000080
#define LXW_COLOR_ORANGE    0xFFA500
#define LXW_COLOR_PINK      0xFFC0CB
#define LXW_COLOR_PURPLE    0x800080
#define LXW_COLOR_RED       0xFF0000
#define LXW_COLOR_SILVER    0xC0C0C0
#define LXW_COLOR_WHITE     0xFFFFFF
#define LXW_COLOR_YELLOW    0xFFFF00

/* Alignment constants */
typedef enum lxw_align {
    LXW_ALIGN_NONE = 0,
    LXW_ALIGN_LEFT,
    LXW_ALIGN_CENTER,
    LXW_ALIGN_RIGHT,
    LXW_ALIGN_FILL,
    LXW_ALIGN_JUSTIFY,
    LXW_ALIGN_CENTER_ACROSS,
    LXW_ALIGN_DISTRIBUTED,
    LXW_ALIGN_VERTICAL_TOP,
    LXW_ALIGN_VERTICAL_BOTTOM,
    LXW_ALIGN_VERTICAL_CENTER,
    LXW_ALIGN_VERTICAL_JUSTIFY,
    LXW_ALIGN_VERTICAL_DISTRIBUTED
} lxw_align;

/* Error codes */
typedef enum lxw_error {
    LXW_NO_ERROR = 0,
    LXW_ERROR_MEMORY_MALLOC_FAILED,
    LXW_ERROR_CREATING_XLSX_FILE,
    LXW_ERROR_CREATING_TMPFILE,
    LXW_ERROR_READING_TMPFILE,
    LXW_ERROR_ZIP_FILE_OPERATION,
    LXW_ERROR_ZIP_FILE_ADD,
    LXW_ERROR_ZIP_CLOSE,
    LXW_ERROR_FEATURE_NOT_SUPPORTED,
    LXW_ERROR_NULL_PARAMETER_IGNORED,
    LXW_ERROR_PARAMETER_VALIDATION,
    LXW_ERROR_SHEETNAME_LENGTH_EXCEEDED,
    LXW_ERROR_INVALID_SHEETNAME_CHARACTER,
    LXW_ERROR_SHEETNAME_START_END_APOSTROPHE,
    LXW_ERROR_SHEETNAME_ALREADY_USED,
    LXW_ERROR_32_STRING_LENGTH_EXCEEDED,
    LXW_ERROR_128_STRING_LENGTH_EXCEEDED,
    LXW_ERROR_255_STRING_LENGTH_EXCEEDED,
    LXW_ERROR_MAX_STRING_LENGTH_EXCEEDED,
    LXW_ERROR_SHARED_STRING_INDEX_NOT_FOUND,
    LXW_ERROR_WORKSHEET_INDEX_OUT_OF_RANGE,
    LXW_ERROR_WORKSHEET_MAX_NUMBER_URLS_EXCEEDED,
    LXW_ERROR_WORKSHEET_MAX_URL_LENGTH_EXCEEDED
} lxw_error;

/* Forward declarations */
typedef struct lxw_workbook lxw_workbook;
typedef struct lxw_worksheet lxw_worksheet;
typedef struct lxw_format lxw_format;
typedef struct lxw_chart lxw_chart;
typedef struct lxw_chartsheet lxw_chartsheet;

/* Row and column types */
typedef uint32_t lxw_row_t;
typedef uint16_t lxw_col_t;

/* Workbook functions */
lxw_workbook *workbook_new(const char *filename);
lxw_workbook *workbook_new_opt(const char *filename, lxw_workbook_options *options);
lxw_worksheet *workbook_add_worksheet(lxw_workbook *workbook, const char *sheetname);
lxw_format *workbook_add_format(lxw_workbook *workbook);
lxw_error workbook_close(lxw_workbook *workbook);
lxw_worksheet *workbook_get_worksheet_by_name(lxw_workbook *workbook, const char *name);

/* Worksheet functions */
lxw_error worksheet_write_string(lxw_worksheet *worksheet, lxw_row_t row, lxw_col_t col, const char *string, lxw_format *format);
lxw_error worksheet_write_number(lxw_worksheet *worksheet, lxw_row_t row, lxw_col_t col, double number, lxw_format *format);
lxw_error worksheet_write_datetime(lxw_worksheet *worksheet, lxw_row_t row, lxw_col_t col, lxw_datetime *datetime, lxw_format *format);
lxw_error worksheet_write_url(lxw_worksheet *worksheet, lxw_row_t row, lxw_col_t col, const char *url, lxw_format *format);
lxw_error worksheet_write_formula(lxw_worksheet *worksheet, lxw_row_t row, lxw_col_t col, const char *formula, lxw_format *format);
lxw_error worksheet_write_blank(lxw_worksheet *worksheet, lxw_row_t row, lxw_col_t col, lxw_format *format);
lxw_error worksheet_write_boolean(lxw_worksheet *worksheet, lxw_row_t row, lxw_col_t col, int value, lxw_format *format);

/* Format functions */
void format_set_font_name(lxw_format *format, const char *font_name);
void format_set_font_size(lxw_format *format, double size);
void format_set_font_color(lxw_format *format, lxw_color_t color);
void format_set_bold(lxw_format *format);
void format_set_italic(lxw_format *format);
void format_set_underline(lxw_format *format, uint8_t style);
void format_set_font_strikeout(lxw_format *format);
void format_set_font_script(lxw_format *format, uint8_t style);
void format_set_num_format(lxw_format *format, const char *num_format);
void format_set_num_format_index(lxw_format *format, uint8_t index);
void format_set_unlocked(lxw_format *format);
void format_set_hidden(lxw_format *format);
void format_set_align(lxw_format *format, uint8_t alignment);
void format_set_text_wrap(lxw_format *format);
void format_set_rotation(lxw_format *format, int16_t angle);
void format_set_indent(lxw_format *format, uint8_t level);
void format_set_shrink(lxw_format *format);
void format_set_pattern(lxw_format *format, uint8_t index);
void format_set_bg_color(lxw_format *format, lxw_color_t color);
void format_set_fg_color(lxw_format *format, lxw_color_t color);
void format_set_border(lxw_format *format, uint8_t style);
void format_set_bottom(lxw_format *format, uint8_t style);
void format_set_top(lxw_format *format, uint8_t style);
void format_set_left(lxw_format *format, uint8_t style);
void format_set_right(lxw_format *format, uint8_t style);
void format_set_border_color(lxw_format *format, lxw_color_t color);
void format_set_bottom_color(lxw_format *format, lxw_color_t color);
void format_set_top_color(lxw_format *format, lxw_color_t color);
void format_set_left_color(lxw_format *format, lxw_color_t color);
void format_set_right_color(lxw_format *format, lxw_color_t color);

/* Color type */
typedef uint32_t lxw_color_t;

/* Date/time structure */
typedef struct lxw_datetime {
    int year;
    int month;
    int day;
    int hour;
    int min;
    double sec;
} lxw_datetime;

/* Workbook options */
typedef struct lxw_workbook_options {
    uint8_t constant_memory;
    char *tmpdir;
    uint8_t use_zip64;
} lxw_workbook_options;

#ifdef __cplusplus
}
#endif

#endif /* XLSXWRITER_H */
