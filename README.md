# Student Management System with Excel Integration

A high-performance C++ program for managing student records using xlsxwriter library to store data in Excel files (.xlsx) with advanced testing capabilities.

## Features

- ✅ **Create new Excel files automatically**
- ✅ **Add new student records** (ID, Name, Age, Department)
- ✅ **View all entered records** in formatted table
- ✅ **Automatic data saving** to Excel format
- ✅ **Mass file creation** (200 files) for testing
- ✅ **Bulk delete functionality** with confirmation
- ✅ **English-language interface**
- ✅ **Comprehensive error handling**
- ✅ **Input validation** for all user data
- ✅ **Modern C++17 features**

## System Requirements

- **C++ Compiler**: g++ with C++17 support
- **Operating System**: Windows, Linux, or macOS
- **Memory**: Minimum 512MB RAM
- **Storage**: 50MB free space (more for test files)

## Installation & Setup

### Windows

1. **Install MinGW-w64** (if not already installed):
   - Download from: https://www.mingw-w64.org/
   - Add to PATH environment variable

2. **Build the project**:
   ```cmd
   build.bat
   ```

3. **Run the program**:
   ```cmd
   student_manager.exe
   ```

### Linux/macOS

1. **Install g++** (if not already installed):
   ```bash
   # Ubuntu/Debian
   sudo apt install g++
   
   # CentOS/RHEL
   sudo yum install gcc-c++
   
   # macOS
   xcode-select --install
   ```

2. **Build the project**:
   ```bash
   chmod +x build.sh
   ./build.sh
   ```

3. **Run the program**:
   ```bash
   ./student_manager
   ```

## Manual Compilation

If you prefer to compile manually:

```bash
g++ -std=c++17 -Wall -Wextra -O2 -o student_manager student_manager.cpp xlsxwriter.cpp -I.
```

## Usage Guide

### Main Menu Options

1. **Add new student**
   - Enter student name (1-50 characters)
   - Enter age (16-100 years)
   - Enter department (1-30 characters)
   - System automatically assigns unique ID

2. **View all students**
   - Displays formatted table of all students
   - Shows ID, Name, Age, and Department
   - Includes total student count

3. **Create 200 test files**
   - Generates 200 Excel files for testing
   - Files named: students_1.xlsx to students_200.xlsx
   - Each file contains sample student data
   - Progress indicator shows creation status

4. **Delete all student files**
   - **WARNING**: Permanently deletes all student Excel files
   - Requires confirmation (y/N)
   - Shows deletion progress and results
   - Only deletes files containing "student" in filename

5. **Save and exit**
   - Saves all current students to students.xlsx
   - Gracefully exits the program

### Input Validation

- **Name**: 1-50 characters, no empty strings
- **Age**: Integer between 16-100
- **Department**: 1-30 characters, no empty strings
- **Menu choices**: Numbers 1-5 only

### Error Handling

- Invalid input automatically prompts for re-entry
- File operation errors are caught and reported
- Memory allocation failures are handled gracefully
- Filesystem errors during bulk operations are logged

## File Structure

```
student_management_system/
├── student_manager.cpp    # Main program source
├── xlsxwriter.cpp        # Excel library implementation
├── xlsxwriter.h          # Excel library header
├── build.bat             # Windows build script
├── build.sh              # Linux/macOS build script
├── README.md             # This documentation
└── students.xlsx         # Generated when saving data
```

## Output Files

- **students.xlsx**: Main student database file
- **students_1.xlsx to students_200.xlsx**: Test files (when created)

## Technical Details

### Libraries Used
- **Standard C++ Libraries**: iostream, fstream, string, vector, filesystem
- **Custom xlsxwriter**: Simplified Excel file creation library

### Performance Features
- Efficient memory usage for mass file operations
- Optimized file I/O operations
- Resource cleanup guarantees
- Input validation to prevent crashes

### Code Quality
- Modern C++17 features
- Comprehensive error handling
- Clean, maintainable code structure
- Extensive input validation
- Memory-safe operations

## Testing

### Basic Testing
1. Add several students with various data
2. View the student list
3. Save and verify Excel file creation
4. Restart program and verify data persistence

### Advanced Testing
1. Create 200 test files and verify creation
2. Test bulk deletion functionality
3. Test input validation with invalid data
4. Test error handling with file permission issues

### Performance Testing
- Mass file creation: ~200 files in under 30 seconds
- Memory usage: <50MB during normal operation
- File size: ~8KB per Excel file with headers

## Troubleshooting

### Common Issues

**Build Errors:**
- Ensure g++ is installed and in PATH
- Verify all source files are present
- Check C++17 support in compiler

**Runtime Errors:**
- Check file permissions in current directory
- Ensure sufficient disk space for file operations
- Verify filesystem supports long filenames

**Excel File Issues:**
- Files are saved in simplified Excel format
- Compatible with most spreadsheet applications
- Use Excel or LibreOffice to view files

## License

This project is provided as-is for educational and commercial use.

## Support

For issues or questions:
1. Check this README for common solutions
2. Verify system requirements are met
3. Test with minimal data first
4. Check file permissions and disk space

---

**Version**: 1.0  
**Last Updated**: 2025-06-25  
**Compatibility**: C++17, Windows/Linux/macOS
