#include <iostream>
#include <string>
#include <vector>
#include <random>

// Test the new Arabic names functionality
std::vector<std::string> first_names = {
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON>", "<PERSON>", "Mostaf<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>d", "Far<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"
};

st<PERSON>::vector<st<PERSON>::string> last_names = {
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON><PERSON>", "<PERSON> <PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>", "<PERSON> <PERSON><PERSON><PERSON>", "<PERSON> Naggar",
    "Farouk", "Mansour", "Rashid", "Salim", "Zaki", "Nasser", "Fouad", "Saad", "Helmy", "Gaber",
    "Morsy", "Soliman", "Ramadan", "Shahin", "Badawi", "Tantawy", "Shehata", "Metwally",
    "Al Zahra", "Al Rashid", "Al Masri", "Al Shami", "Al Najjar", "Al Khatib", "Al Sabbagh",
    "Qasemi", "Hashemi", "Ansari", "Baghdadi", "Damasceni", "Halabi", "Hijazi", "Yamani"
};

std::vector<std::string> departments = {
    "Computer Science", "Engineering", "Medicine", "Business Administration", "Law",
    "Pharmacy", "Dentistry", "Architecture", "Economics", "Psychology",
    "Mathematics", "Physics", "Chemistry", "Biology", "Literature",
    "History", "Geography", "Philosophy", "Sociology", "Political Science"
};

std::random_device rd;
std::mt19937 gen(rd());

std::string generate_random_name() {
    std::uniform_int_distribution<> first_dist(0, first_names.size() - 1);
    std::uniform_int_distribution<> last_dist(0, last_names.size() - 1);
    return first_names[first_dist(gen)] + " " + last_names[last_dist(gen)];
}

int generate_random_age() {
    std::uniform_int_distribution<> age_dist(18, 25);
    return age_dist(gen);
}

std::string generate_random_department() {
    std::uniform_int_distribution<> dept_dist(0, departments.size() - 1);
    return departments[dept_dist(gen)];
}

int main() {
    std::cout << "=== Testing New Arabic Names Feature ===\n\n";
    
    std::cout << "Generating 10 random Arabic students:\n";
    std::cout << std::string(50, '-') << "\n";
    
    for (int i = 1; i <= 10; ++i) {
        std::string name = generate_random_name();
        int age = generate_random_age();
        std::string department = generate_random_department();
        
        std::cout << i << ". " << name << " (Age: " << age 
                  << ", Dept: " << department << ")\n";
    }
    
    std::cout << std::string(50, '-') << "\n";
    std::cout << "Test completed successfully!\n";
    std::cout << "The new feature generates realistic Arabic names in English letters.\n";
    
    return 0;
}
