========================================
    قالب إنشاء برامج إدارة البيانات مع Excel
========================================

هذا القالب يمكن استخدامه لإنشاء برامج مشابهة لنظام إدارة الطلاب
يدعم أي نوع من البيانات مع تكامل Excel وواجهة تفاعلية

========================================
1. هيكل المشروع الأساسي
========================================

1.1 الملفات المطلوبة:
--------------------
□ main_program.cpp        - البرنامج الرئيسي
□ xlsxwriter.h           - مكتبة Excel (رأس)
□ xlsxwriter.cpp         - مكتبة Excel (تنفيذ)
□ test_program.cpp       - اختبارات المكتبة
□ build.bat             - بناء Windows
□ build.sh              - بناء Unix/Linux
□ Makefile              - نظام بناء متقدم
□ README.md             - دليل المستخدم
□ config.json           - ملف التكوين

1.2 المجلدات المقترحة:
---------------------
□ src/                  - ملفات المصدر
□ include/              - ملفات الرأس
□ build/                - ملفات البناء
□ data/                 - ملفات البيانات
□ tests/                - ملفات الاختبار
□ docs/                 - التوثيق

========================================
2. قالب هيكل البيانات
========================================

2.1 قالب الهيكل الأساسي:
------------------------
struct [ENTITY_NAME] {
    int id;                    // معرف فريد
    string [FIELD1_NAME];      // حقل نصي
    int [FIELD2_NAME];         // حقل رقمي
    string [FIELD3_NAME];      // حقل نصي إضافي
    // إضافة المزيد من الحقول حسب الحاجة
};

2.2 أمثلة للتطبيق:
-----------------
• نظام إدارة الموظفين:
  struct Employee {
      int id;
      string name;
      int salary;
      string department;
      string position;
  };

• نظام إدارة المنتجات:
  struct Product {
      int id;
      string name;
      double price;
      string category;
      int quantity;
  };

• نظام إدارة العملاء:
  struct Customer {
      int id;
      string name;
      string phone;
      string email;
      string address;
  };

========================================
3. قالب الوظائف الأساسية
========================================

3.1 وظائف إدارة البيانات:
-------------------------
□ add_[entity]()           - إضافة عنصر جديد
□ view_all_[entities]()    - عرض جميع العناصر
□ search_[entity]()        - البحث عن عنصر
□ update_[entity]()        - تحديث عنصر
□ delete_[entity]()        - حذف عنصر
□ save_to_excel()          - حفظ في Excel
□ load_from_excel()        - تحميل من Excel

3.2 وظائف إدارة الملفات:
------------------------
□ create_test_files()      - إنشاء ملفات اختبار
□ delete_all_files()       - حذف جميع الملفات
□ backup_data()            - نسخ احتياطي
□ restore_data()           - استرداد البيانات
□ export_to_csv()          - تصدير CSV
□ import_from_csv()        - استيراد CSV

3.3 وظائف المساعدة:
------------------
□ validate_input()         - التحقق من الإدخال
□ clear_screen()           - تنظيف الشاشة
□ pause_system()           - انتظار المستخدم
□ show_menu()              - عرض القائمة
□ get_user_choice()        - الحصول على اختيار المستخدم

========================================
4. قالب واجهة المستخدم
========================================

4.1 القائمة الرئيسية:
--------------------
========================================
    [SYSTEM_NAME] Management System
========================================
1. Add new [entity]
2. View all [entities]
3. Search [entity]
4. Update [entity]
5. Delete [entity]
6. Create test files
7. Delete all files
8. Export data
9. Import data
10. Save and exit
----------------------------------------
Enter your choice (1-10):

4.2 نموذج إدخال البيانات:
-------------------------
=== Add New [Entity] ===
Enter [field1]: [input]
Enter [field2]: [input]
Enter [field3]: [input]
[Entity] added successfully! (ID: [id])

4.3 نموذج عرض البيانات:
-----------------------
=== All [Entities] ===
ID   [Field1]             [Field2]  [Field3]
-------------------------------------------------------
1    [data1]              [data2]   [data3]
2    [data1]              [data2]   [data3]

Total [entities]: [count]

========================================
5. قالب ملف التكوين (config.json)
========================================

5.1 إعدادات النظام:
------------------
{
  "system": {
    "name": "[SYSTEM_NAME]",
    "version": "1.0.0",
    "author": "[AUTHOR_NAME]",
    "description": "[SYSTEM_DESCRIPTION]"
  },
  "entity": {
    "name": "[ENTITY_NAME]",
    "plural": "[ENTITIES_NAME]",
    "fields": [
      {
        "name": "[FIELD1_NAME]",
        "type": "string",
        "max_length": 50,
        "required": true
      },
      {
        "name": "[FIELD2_NAME]",
        "type": "int",
        "min_value": 1,
        "max_value": 1000,
        "required": true
      }
    ]
  },
  "files": {
    "data_file": "[entities].xlsx",
    "backup_prefix": "backup_",
    "test_files_count": 200
  },
  "ui": {
    "language": "english",
    "clear_screen": true,
    "show_progress": true
  }
}

========================================
6. قالب كود C++ الأساسي
========================================

6.1 رأس الملف:
--------------
#include <iostream>
#include <vector>
#include <string>
#include <fstream>
#include <algorithm>
#include <iomanip>
#include <filesystem>
#include "xlsxwriter.h"

using namespace std;
namespace fs = std::filesystem;

6.2 المتغيرات العامة:
--------------------
vector<[ENTITY_NAME]> [entities];
int next_id = 1;
const string DATA_FILE = "[entities].xlsx";
const string FILE_PREFIX = "[entities]_";

6.3 قالب الوظيفة الرئيسية:
--------------------------
int main() {
    cout << "Welcome to [SYSTEM_NAME] Management System!" << endl;
    cout << "Initializing system..." << endl;
    
    int choice;
    do {
        show_menu();
        choice = get_user_choice();
        
        switch(choice) {
            case 1: add_[entity](); break;
            case 2: view_all_[entities](); break;
            case 3: search_[entity](); break;
            case 4: update_[entity](); break;
            case 5: delete_[entity](); break;
            case 6: create_test_files(); break;
            case 7: delete_all_files(); break;
            case 8: export_data(); break;
            case 9: import_data(); break;
            case 10: 
                save_to_excel();
                cout << "Thank you for using [SYSTEM_NAME]!" << endl;
                break;
            default:
                cout << "Invalid choice! Please try again." << endl;
        }
    } while(choice != 10);
    
    return 0;
}

========================================
7. قالب نظام البناء
========================================

7.1 قالب build.bat:
------------------
@echo off
echo Building [SYSTEM_NAME] Management System...

if exist "g++.exe" (
    g++ -std=c++17 -Wall -Wextra -O2 -I. main_program.cpp xlsxwriter.cpp -o [program_name].exe
) else if exist "clang++.exe" (
    clang++ -std=c++17 -Wall -Wextra -O2 -I. main_program.cpp xlsxwriter.cpp -o [program_name].exe
) else (
    echo Error: No suitable C++ compiler found!
    pause
    exit /b 1
)

if %ERRORLEVEL% EQU 0 (
    echo Build successful! Executable: [program_name].exe
) else (
    echo Build failed!
)
pause

7.2 قالب Makefile:
-----------------
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2 -I.
TARGET = [program_name]
SOURCES = main_program.cpp xlsxwriter.cpp
OBJECTS = $(SOURCES:.cpp=.o)

all: $(TARGET)

$(TARGET): $(OBJECTS)
	$(CXX) $(OBJECTS) -o $(TARGET)

%.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

clean:
	rm -f $(OBJECTS) $(TARGET)

test: $(TARGET)
	./$(TARGET)

.PHONY: all clean test

========================================
8. خطوات إنشاء مشروع جديد
========================================

8.1 التحضير:
------------
□ تحديد نوع البيانات المراد إدارتها
□ تحديد الحقول المطلوبة
□ اختيار اسم النظام
□ إعداد بيئة التطوير

8.2 التنفيذ:
-----------
□ نسخ ملفات القالب
□ استبدال المتغيرات بالقيم المطلوبة
□ تخصيص هيكل البيانات
□ تعديل واجهة المستخدم
□ إضافة وظائف مخصصة

8.3 الاختبار:
------------
□ كومبايل المشروع
□ اختبار جميع الوظائف
□ التحقق من إنشاء الملفات
□ اختبار معالجة الأخطاء
□ اختبار الأداء

8.4 النشر:
----------
□ إنشاء دليل المستخدم
□ إعداد ملفات التثبيت
□ اختبار على أنظمة مختلفة
□ إنشاء النسخة النهائية

========================================
9. متغيرات القالب للاستبدال
========================================

[SYSTEM_NAME]        - اسم النظام
[ENTITY_NAME]        - اسم الكيان (مفرد)
[ENTITIES_NAME]      - اسم الكيان (جمع)
[FIELD1_NAME]        - اسم الحقل الأول
[FIELD2_NAME]        - اسم الحقل الثاني
[FIELD3_NAME]        - اسم الحقل الثالث
[PROGRAM_NAME]       - اسم البرنامج التنفيذي
[AUTHOR_NAME]        - اسم المطور
[SYSTEM_DESCRIPTION] - وصف النظام

========================================
10. أمثلة للتطبيق
========================================

10.1 نظام إدارة المكتبة:
-----------------------
[SYSTEM_NAME] = Library
[ENTITY_NAME] = Book
[ENTITIES_NAME] = Books
[FIELD1_NAME] = title
[FIELD2_NAME] = pages
[FIELD3_NAME] = author

10.2 نظام إدارة المخزون:
------------------------
[SYSTEM_NAME] = Inventory
[ENTITY_NAME] = Item
[ENTITIES_NAME] = Items
[FIELD1_NAME] = name
[FIELD2_NAME] = quantity
[FIELD3_NAME] = category

========================================
انتهى قالب إنشاء البرامج
========================================
