#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <filesystem>
#include <sstream>
#include <limits>
#include <iomanip>
#include "xlsxwriter.h"

// Student structure
struct Student {
    int id;
    std::string name;
    int age;
    std::string department;
    
    Student() : id(0), age(0) {}
    Student(int id, const std::string& name, int age, const std::string& department)
        : id(id), name(name), age(age), department(department) {}
};

// Global variables
std::vector<Student> students;
int next_student_id = 1;

// Function declarations
lxw_workbook* init_excel_file(const std::string& filename);
void add_student();
void view_all_students();
void create_test_files();
void delete_all_files();
void show_main_menu();
void save_students_to_excel(const std::string& filename);
void load_students_from_file();
bool validate_student_input(const std::string& name, int age, const std::string& department);
void clear_input_buffer();

// Initialize Excel file with headers
lxw_workbook* init_excel_file(const std::string& filename) {
    lxw_workbook* wb = workbook_new(filename.c_str());
    if (!wb) {
        std::cerr << "Error: Could not create workbook " << filename << std::endl;
        return nullptr;
    }
    
    lxw_worksheet* ws = workbook_add_worksheet(wb, "Students");
    if (!ws) {
        std::cerr << "Error: Could not add worksheet" << std::endl;
        workbook_close(wb);
        return nullptr;
    }
    
    // Create header format
    lxw_format* header_fmt = workbook_add_format(wb);
    format_set_bold(header_fmt);
    format_set_align(header_fmt, LXW_ALIGN_CENTER);
    format_set_bg_color(header_fmt, LXW_COLOR_GRAY);
    
    // Write headers
    worksheet_write_string(ws, 0, 0, "ID", header_fmt);
    worksheet_write_string(ws, 0, 1, "Name", header_fmt);
    worksheet_write_string(ws, 0, 2, "Age", header_fmt);
    worksheet_write_string(ws, 0, 3, "Department", header_fmt);
    
    return wb;
}

// Clear input buffer to handle invalid input
void clear_input_buffer() {
    std::cin.clear();
    std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
}

// Validate student input
bool validate_student_input(const std::string& name, int age, const std::string& department) {
    if (name.empty() || name.length() > 50) {
        std::cout << "Error: Name must be between 1 and 50 characters.\n";
        return false;
    }
    
    if (age < 16 || age > 100) {
        std::cout << "Error: Age must be between 16 and 100.\n";
        return false;
    }
    
    if (department.empty() || department.length() > 30) {
        std::cout << "Error: Department must be between 1 and 30 characters.\n";
        return false;
    }
    
    return true;
}

// Add new student
void add_student() {
    std::string name, department;
    int age;
    
    std::cout << "\n=== Add New Student ===\n";
    
    // Get student name
    std::cout << "Enter student name: ";
    std::cin.ignore();
    std::getline(std::cin, name);
    
    // Get student age with validation
    while (true) {
        std::cout << "Enter student age (16-100): ";
        if (std::cin >> age) {
            break;
        } else {
            std::cout << "Error: Please enter a valid number.\n";
            clear_input_buffer();
        }
    }
    
    // Get department
    std::cout << "Enter department: ";
    std::cin.ignore();
    std::getline(std::cin, department);
    
    // Validate input
    if (!validate_student_input(name, age, department)) {
        std::cout << "Student not added due to validation errors.\n";
        return;
    }
    
    // Create and add student
    Student new_student(next_student_id++, name, age, department);
    students.push_back(new_student);
    
    std::cout << "Student added successfully! (ID: " << new_student.id << ")\n";
}

// View all students
void view_all_students() {
    std::cout << "\n=== All Students ===\n";
    
    if (students.empty()) {
        std::cout << "No students found.\n";
        return;
    }
    
    std::cout << std::left << std::setw(5) << "ID" 
              << std::setw(25) << "Name" 
              << std::setw(5) << "Age" 
              << std::setw(20) << "Department" << std::endl;
    std::cout << std::string(55, '-') << std::endl;
    
    for (const auto& student : students) {
        std::cout << std::left << std::setw(5) << student.id
                  << std::setw(25) << student.name
                  << std::setw(5) << student.age
                  << std::setw(20) << student.department << std::endl;
    }
    
    std::cout << "\nTotal students: " << students.size() << std::endl;
}

// Save students to Excel file
void save_students_to_excel(const std::string& filename) {
    lxw_workbook* wb = init_excel_file(filename);
    if (!wb) {
        std::cerr << "Error: Could not create Excel file " << filename << std::endl;
        return;
    }
    
    lxw_worksheet* ws = workbook_get_worksheet_by_name(wb, "Students");
    if (!ws) {
        std::cerr << "Error: Could not get worksheet" << std::endl;
        workbook_close(wb);
        return;
    }
    
    // Write student data
    for (size_t i = 0; i < students.size(); ++i) {
        int row = static_cast<int>(i + 1); // Start from row 1 (after headers)
        const Student& student = students[i];
        
        worksheet_write_number(ws, row, 0, student.id, nullptr);
        worksheet_write_string(ws, row, 1, student.name.c_str(), nullptr);
        worksheet_write_number(ws, row, 2, student.age, nullptr);
        worksheet_write_string(ws, row, 3, student.department.c_str(), nullptr);
    }
    
    lxw_error result = workbook_close(wb);
    if (result != LXW_NO_ERROR) {
        std::cerr << "Error: Could not save Excel file " << filename << std::endl;
    } else {
        std::cout << "Data saved to " << filename << " successfully!\n";
    }
}

// Create 200 test files
void create_test_files() {
    std::cout << "\n=== Creating 200 Test Files ===\n";
    std::cout << "This may take a moment...\n";
    
    int created_count = 0;
    int failed_count = 0;
    
    for (int i = 1; i <= 200; ++i) {
        std::string filename = "students_" + std::to_string(i) + ".xlsx";
        
        lxw_workbook* wb = init_excel_file(filename);
        if (wb) {
            // Add some sample data to each file
            lxw_worksheet* ws = workbook_get_worksheet_by_name(wb, "Students");
            if (ws) {
                worksheet_write_number(ws, 1, 0, i, nullptr);
                worksheet_write_string(ws, 1, 1, ("Student_" + std::to_string(i)).c_str(), nullptr);
                worksheet_write_number(ws, 1, 2, 20 + (i % 10), nullptr);
                worksheet_write_string(ws, 1, 3, ("Dept_" + std::to_string((i % 5) + 1)).c_str(), nullptr);
            }
            
            lxw_error result = workbook_close(wb);
            if (result == LXW_NO_ERROR) {
                created_count++;
            } else {
                failed_count++;
            }
        } else {
            failed_count++;
        }
        
        // Show progress every 50 files
        if (i % 50 == 0) {
            std::cout << "Created " << i << " files...\n";
        }
    }
    
    std::cout << "Test file creation completed!\n";
    std::cout << "Successfully created: " << created_count << " files\n";
    if (failed_count > 0) {
        std::cout << "Failed to create: " << failed_count << " files\n";
    }
}

// Delete all student files
void delete_all_files() {
    std::cout << "\n=== Delete All Student Files ===\n";
    std::cout << "WARNING: This will delete all student Excel files in the current directory.\n";
    std::cout << "Are you sure you want to continue? (y/N): ";
    
    char confirm;
    std::cin >> confirm;
    
    if (confirm != 'y' && confirm != 'Y') {
        std::cout << "Operation cancelled.\n";
        return;
    }
    
    int deleted_count = 0;
    int failed_count = 0;
    
    try {
        for (const auto& entry : std::filesystem::directory_iterator(".")) {
            if (entry.is_regular_file()) {
                std::string filename = entry.path().filename().string();
                
                // Check if it's a student file (contains "student" and ends with .xlsx)
                if ((filename.find("student") != std::string::npos || 
                     filename.find("Student") != std::string::npos) && 
                    filename.substr(filename.length() - 5) == ".xlsx") {
                    
                    try {
                        std::filesystem::remove(entry.path());
                        deleted_count++;
                        std::cout << "Deleted: " << filename << std::endl;
                    } catch (const std::filesystem::filesystem_error& e) {
                        std::cerr << "Failed to delete " << filename << ": " << e.what() << std::endl;
                        failed_count++;
                    }
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "Error accessing directory: " << e.what() << std::endl;
        return;
    }
    
    std::cout << "\nDeletion completed!\n";
    std::cout << "Successfully deleted: " << deleted_count << " files\n";
    if (failed_count > 0) {
        std::cout << "Failed to delete: " << failed_count << " files\n";
    }
}

// Show main menu
void show_main_menu() {
    std::cout << "\n" << std::string(40, '=') << std::endl;
    std::cout << "    Student Management System (Excel)" << std::endl;
    std::cout << std::string(40, '=') << std::endl;
    std::cout << "1. Add new student" << std::endl;
    std::cout << "2. View all students" << std::endl;
    std::cout << "3. Create 200 test files" << std::endl;
    std::cout << "4. Delete all student files" << std::endl;
    std::cout << "5. Save and exit" << std::endl;
    std::cout << std::string(40, '-') << std::endl;
    std::cout << "Enter your choice (1-5): ";
}

// Main function
int main() {
    std::cout << "Welcome to Student Management System!" << std::endl;
    std::cout << "Initializing system..." << std::endl;
    
    int choice;
    
    while (true) {
        show_main_menu();
        
        if (!(std::cin >> choice)) {
            std::cout << "Error: Please enter a valid number (1-5).\n";
            clear_input_buffer();
            continue;
        }
        
        switch (choice) {
            case 1:
                add_student();
                break;
            case 2:
                view_all_students();
                break;
            case 3:
                create_test_files();
                break;
            case 4:
                delete_all_files();
                break;
            case 5:
                if (!students.empty()) {
                    save_students_to_excel("students.xlsx");
                }
                std::cout << "Thank you for using Student Management System!\n";
                std::cout << "Goodbye!\n";
                return 0;
            default:
                std::cout << "Invalid choice! Please enter a number between 1 and 5.\n";
        }
        
        // Pause before showing menu again
        std::cout << "\nPress Enter to continue...";
        std::cin.ignore();
        std::cin.get();
    }
    
    return 0;
}
