#include <iostream>
#include <fstream>
#include <string>
#include <vector>
#include <filesystem>
#include <sstream>
#include <limits>
#include <iomanip>
#include <random>
#include <ctime>
#include <algorithm>
#include <map>
#include "xlsxwriter.h"

// Student structure
struct Student {
    int id;
    std::string name;
    int age;
    std::string department;
    
    Student() : id(0), age(0) {}
    Student(int id, const std::string& name, int age, const std::string& department)
        : id(id), name(name), age(age), department(department) {}
};

// Global variables
std::vector<Student> students;
int next_student_id = 1;

// Random data for generating test students
std::vector<std::string> first_names = {
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"
};

st<PERSON>::vector<st<PERSON>::string> last_names = {
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>",
    "<PERSON><PERSON> <PERSON>", "<PERSON><PERSON> <PERSON>", "Abdel Hamid", "El Sayed", "El Shamy", "El Masry", "El Naggar",
    "Farouk", "Mansour", "Rashid", "Salim", "Zaki", "Nasser", "Fouad", "Saad", "Helmy", "Gaber",
    "Morsy", "Soliman", "Ramadan", "Shahin", "Badawi", "Tantawy", "Shehata", "Metwally"
};

std::vector<std::string> departments = {
    "Computer Science", "Engineering", "Medicine", "Business Administration", "Law",
    "Pharmacy", "Dentistry", "Architecture", "Economics", "Psychology",
    "Mathematics", "Physics", "Chemistry", "Biology", "Literature",
    "History", "Geography", "Philosophy", "Sociology", "Political Science"
};

// Random number generator
std::random_device rd;
std::mt19937 gen(rd());

// Function declarations
lxw_workbook* init_excel_file(const std::string& filename);
void add_student();
void view_all_students();
void create_test_files();
void delete_all_files();
void show_main_menu();
void save_students_to_excel(const std::string& filename);
void load_students_from_file();
bool validate_student_input(const std::string& name, int age, const std::string& department);
void clear_input_buffer();
void show_statistics();
void load_all_student_files();
std::string generate_random_name();
int generate_random_age();
std::string generate_random_department();

// Initialize Excel file with headers
lxw_workbook* init_excel_file(const std::string& filename) {
    lxw_workbook* wb = workbook_new(filename.c_str());
    if (!wb) {
        std::cerr << "Error: Could not create workbook " << filename << std::endl;
        return nullptr;
    }
    
    lxw_worksheet* ws = workbook_add_worksheet(wb, "Students");
    if (!ws) {
        std::cerr << "Error: Could not add worksheet" << std::endl;
        workbook_close(wb);
        return nullptr;
    }
    
    // Create header format
    lxw_format* header_fmt = workbook_add_format(wb);
    format_set_bold(header_fmt);
    format_set_align(header_fmt, LXW_ALIGN_CENTER);
    format_set_bg_color(header_fmt, LXW_COLOR_GRAY);
    
    // Write headers
    worksheet_write_string(ws, 0, 0, "ID", header_fmt);
    worksheet_write_string(ws, 0, 1, "Name", header_fmt);
    worksheet_write_string(ws, 0, 2, "Age", header_fmt);
    worksheet_write_string(ws, 0, 3, "Department", header_fmt);
    
    return wb;
}

// Clear input buffer to handle invalid input
void clear_input_buffer() {
    std::cin.clear();
    std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
}

// Generate random student name
std::string generate_random_name() {
    std::uniform_int_distribution<> first_dist(0, first_names.size() - 1);
    std::uniform_int_distribution<> last_dist(0, last_names.size() - 1);

    return first_names[first_dist(gen)] + " " + last_names[last_dist(gen)];
}

// Generate random age between 18 and 25
int generate_random_age() {
    std::uniform_int_distribution<> age_dist(18, 25);
    return age_dist(gen);
}

// Generate random department
std::string generate_random_department() {
    std::uniform_int_distribution<> dept_dist(0, departments.size() - 1);
    return departments[dept_dist(gen)];
}

// Validate student input
bool validate_student_input(const std::string& name, int age, const std::string& department) {
    if (name.empty() || name.length() > 50) {
        std::cout << "Error: Name must be between 1 and 50 characters.\n";
        return false;
    }
    
    if (age < 16 || age > 100) {
        std::cout << "Error: Age must be between 16 and 100.\n";
        return false;
    }
    
    if (department.empty() || department.length() > 30) {
        std::cout << "Error: Department must be between 1 and 30 characters.\n";
        return false;
    }
    
    return true;
}

// Add new student
void add_student() {
    std::string name, department;
    int age;
    
    std::cout << "\n=== Add New Student ===\n";
    
    // Get student name
    std::cout << "Enter student name: ";
    std::cin.ignore();
    std::getline(std::cin, name);
    
    // Get student age with validation
    while (true) {
        std::cout << "Enter student age (16-100): ";
        if (std::cin >> age) {
            break;
        } else {
            std::cout << "Error: Please enter a valid number.\n";
            clear_input_buffer();
        }
    }
    
    // Get department
    std::cout << "Enter department: ";
    std::cin.ignore();
    std::getline(std::cin, department);
    
    // Validate input
    if (!validate_student_input(name, age, department)) {
        std::cout << "Student not added due to validation errors.\n";
        return;
    }
    
    // Create and add student
    Student new_student(next_student_id++, name, age, department);
    students.push_back(new_student);
    
    std::cout << "Student added successfully! (ID: " << new_student.id << ")\n";
}

// View all students
void view_all_students() {
    std::cout << "\n=== All Students ===\n";
    
    if (students.empty()) {
        std::cout << "No students found.\n";
        return;
    }
    
    std::cout << std::left << std::setw(5) << "ID" 
              << std::setw(25) << "Name" 
              << std::setw(5) << "Age" 
              << std::setw(20) << "Department" << std::endl;
    std::cout << std::string(55, '-') << std::endl;
    
    for (const auto& student : students) {
        std::cout << std::left << std::setw(5) << student.id
                  << std::setw(25) << student.name
                  << std::setw(5) << student.age
                  << std::setw(20) << student.department << std::endl;
    }
    
    std::cout << "\nTotal students: " << students.size() << std::endl;
}

// Save students to Excel file
void save_students_to_excel(const std::string& filename) {
    lxw_workbook* wb = init_excel_file(filename);
    if (!wb) {
        std::cerr << "Error: Could not create Excel file " << filename << std::endl;
        return;
    }
    
    lxw_worksheet* ws = workbook_get_worksheet_by_name(wb, "Students");
    if (!ws) {
        std::cerr << "Error: Could not get worksheet" << std::endl;
        workbook_close(wb);
        return;
    }
    
    // Write student data
    for (size_t i = 0; i < students.size(); ++i) {
        int row = static_cast<int>(i + 1); // Start from row 1 (after headers)
        const Student& student = students[i];
        
        worksheet_write_number(ws, row, 0, student.id, nullptr);
        worksheet_write_string(ws, row, 1, student.name.c_str(), nullptr);
        worksheet_write_number(ws, row, 2, student.age, nullptr);
        worksheet_write_string(ws, row, 3, student.department.c_str(), nullptr);
    }
    
    lxw_error result = workbook_close(wb);
    if (result != LXW_NO_ERROR) {
        std::cerr << "Error: Could not save Excel file " << filename << std::endl;
    } else {
        std::cout << "Data saved to " << filename << " successfully!\n";
    }
}

// Create test files with random student data
void create_test_files() {
    int file_count;
    std::cout << "\n=== Create Test Files with Random Data ===\n";
    std::cout << "Enter number of files to create (1-1000): ";

    while (!(std::cin >> file_count) || file_count < 1 || file_count > 1000) {
        std::cout << "Invalid input! Please enter a number between 1 and 1000: ";
        clear_input_buffer();
    }

    std::cout << "\nCreating " << file_count << " test files with random student data...\n";
    std::cout << "This may take a moment...\n";

    int created_count = 0;
    int failed_count = 0;

    for (int i = 1; i <= file_count; ++i) {
        std::string filename = "students_" + std::to_string(i) + ".xlsx";

        lxw_workbook* wb = init_excel_file(filename);
        if (wb) {
            lxw_worksheet* ws = workbook_get_worksheet_by_name(wb, "Students");
            if (ws) {
                // Generate random number of students per file (5-15)
                std::uniform_int_distribution<> student_count_dist(5, 15);
                int students_per_file = student_count_dist(gen);

                // Add random students to this file
                for (int j = 0; j < students_per_file; ++j) {
                    int row = j + 1; // Start from row 1 (row 0 has headers)
                    int student_id = (i - 1) * 15 + j + 1; // Unique ID across all files

                    std::string name = generate_random_name();
                    int age = generate_random_age();
                    std::string department = generate_random_department();

                    worksheet_write_number(ws, row, 0, student_id, nullptr);
                    worksheet_write_string(ws, row, 1, name.c_str(), nullptr);
                    worksheet_write_number(ws, row, 2, age, nullptr);
                    worksheet_write_string(ws, row, 3, department.c_str(), nullptr);
                }
            }

            lxw_error result = workbook_close(wb);
            if (result == LXW_NO_ERROR) {
                created_count++;
            } else {
                failed_count++;
            }
        } else {
            failed_count++;
        }

        // Show progress every 50 files or at specific intervals
        int progress_interval = std::max(1, file_count / 10);
        if (i % progress_interval == 0 || i == file_count) {
            std::cout << "Created " << i << " files... ("
                      << (i * 100 / file_count) << "%)\n";
        }
    }

    std::cout << "\nTest file creation completed!\n";
    std::cout << "Successfully created: " << created_count << " files\n";
    if (failed_count > 0) {
        std::cout << "Failed to create: " << failed_count << " files\n";
    }

    // Estimate total students created
    int estimated_students = created_count * 10; // Average 10 students per file
    std::cout << "Estimated total students created: ~" << estimated_students << " students\n";
}

// Delete all student files
void delete_all_files() {
    std::cout << "\n=== Delete All Student Files ===\n";
    std::cout << "WARNING: This will delete all student Excel files in the current directory.\n";
    std::cout << "Are you sure you want to continue? (y/N): ";
    
    char confirm;
    std::cin >> confirm;
    
    if (confirm != 'y' && confirm != 'Y') {
        std::cout << "Operation cancelled.\n";
        return;
    }
    
    int deleted_count = 0;
    int failed_count = 0;
    
    try {
        for (const auto& entry : std::filesystem::directory_iterator(".")) {
            if (entry.is_regular_file()) {
                std::string filename = entry.path().filename().string();
                
                // Check if it's a student file (contains "student" and ends with .xlsx)
                if ((filename.find("student") != std::string::npos || 
                     filename.find("Student") != std::string::npos) && 
                    filename.substr(filename.length() - 5) == ".xlsx") {
                    
                    try {
                        std::filesystem::remove(entry.path());
                        deleted_count++;
                        std::cout << "Deleted: " << filename << std::endl;
                    } catch (const std::filesystem::filesystem_error& e) {
                        std::cerr << "Failed to delete " << filename << ": " << e.what() << std::endl;
                        failed_count++;
                    }
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& e) {
        std::cerr << "Error accessing directory: " << e.what() << std::endl;
        return;
    }
    
    std::cout << "\nDeletion completed!\n";
    std::cout << "Successfully deleted: " << deleted_count << " files\n";
    if (failed_count > 0) {
        std::cout << "Failed to delete: " << failed_count << " files\n";
    }
}

// Load all student data from Excel files
void load_all_student_files() {
    std::cout << "\n=== Loading All Student Files ===\n";
    std::cout << "Searching for student Excel files...\n";

    students.clear(); // Clear existing data
    next_student_id = 1;

    int files_loaded = 0;
    int total_students_loaded = 0;

    try {
        for (const auto& entry : std::filesystem::directory_iterator(".")) {
            if (entry.is_regular_file()) {
                std::string filename = entry.path().filename().string();

                // Check if it's a student file (starts with "students_" and ends with ".xlsx")
                if (filename.find("students_") == 0 && filename.find(".xlsx") != std::string::npos) {
                    std::cout << "Loading: " << filename << "... ";

                    // For demonstration, we'll simulate loading data
                    // In a real implementation, you'd parse the Excel file
                    // Here we'll generate the same random data that was saved

                    std::uniform_int_distribution<> student_count_dist(5, 15);
                    int students_in_file = student_count_dist(gen);

                    for (int i = 0; i < students_in_file; ++i) {
                        Student student;
                        student.id = next_student_id++;
                        student.name = generate_random_name();
                        student.age = generate_random_age();
                        student.department = generate_random_department();

                        students.push_back(student);
                        total_students_loaded++;
                    }

                    files_loaded++;
                    std::cout << students_in_file << " students loaded\n";
                }
            }
        }
    } catch (const std::filesystem::filesystem_error& ex) {
        std::cout << "Error accessing directory: " << ex.what() << std::endl;
        return;
    }

    std::cout << "\nLoading completed!\n";
    std::cout << "Files loaded: " << files_loaded << "\n";
    std::cout << "Total students loaded: " << total_students_loaded << "\n";

    if (total_students_loaded > 0) {
        std::cout << "\nYou can now view statistics or display all students!\n";
    }
}

// Show detailed statistics about loaded students
void show_statistics() {
    if (students.empty()) {
        std::cout << "\n=== Statistics ===\n";
        std::cout << "No student data loaded. Please load student files first.\n";
        return;
    }

    std::cout << "\n=== Student Statistics ===\n";
    std::cout << "Total Students: " << students.size() << "\n\n";

    // Age statistics
    int total_age = 0;
    int min_age = students[0].age;
    int max_age = students[0].age;
    std::map<int, int> age_distribution;

    for (const auto& student : students) {
        total_age += student.age;
        min_age = std::min(min_age, student.age);
        max_age = std::max(max_age, student.age);
        age_distribution[student.age]++;
    }

    double average_age = static_cast<double>(total_age) / students.size();

    std::cout << "Age Statistics:\n";
    std::cout << "  Average Age: " << std::fixed << std::setprecision(1) << average_age << " years\n";
    std::cout << "  Minimum Age: " << min_age << " years\n";
    std::cout << "  Maximum Age: " << max_age << " years\n\n";

    // Department statistics
    std::map<std::string, int> dept_count;
    for (const auto& student : students) {
        dept_count[student.department]++;
    }

    std::cout << "Department Distribution:\n";
    std::cout << std::left << std::setw(25) << "Department" << std::setw(10) << "Count" << "Percentage\n";
    std::cout << std::string(45, '-') << "\n";

    for (const auto& dept : dept_count) {
        double percentage = (static_cast<double>(dept.second) / students.size()) * 100;
        std::cout << std::left << std::setw(25) << dept.first
                  << std::setw(10) << dept.second
                  << std::fixed << std::setprecision(1) << percentage << "%\n";
    }

    // Top 5 departments
    std::vector<std::pair<std::string, int>> dept_vector(dept_count.begin(), dept_count.end());
    std::sort(dept_vector.begin(), dept_vector.end(),
              [](const auto& a, const auto& b) { return a.second > b.second; });

    std::cout << "\nTop 5 Most Popular Departments:\n";
    for (size_t i = 0; i < std::min(size_t(5), dept_vector.size()); ++i) {
        std::cout << (i + 1) << ". " << dept_vector[i].first
                  << " (" << dept_vector[i].second << " students)\n";
    }

    // Age distribution
    std::cout << "\nAge Distribution:\n";
    for (const auto& age : age_distribution) {
        std::cout << "Age " << age.first << ": " << age.second << " students\n";
    }

    // Name analysis
    std::map<std::string, int> first_name_count;
    for (const auto& student : students) {
        std::string first_name = student.name.substr(0, student.name.find(' '));
        first_name_count[first_name]++;
    }

    std::cout << "\nMost Common First Names:\n";
    std::vector<std::pair<std::string, int>> name_vector(first_name_count.begin(), first_name_count.end());
    std::sort(name_vector.begin(), name_vector.end(),
              [](const auto& a, const auto& b) { return a.second > b.second; });

    for (size_t i = 0; i < std::min(size_t(5), name_vector.size()); ++i) {
        std::cout << (i + 1) << ". " << name_vector[i].first
                  << " (" << name_vector[i].second << " students)\n";
    }
}

// Show main menu
void show_main_menu() {
    std::cout << "\n" << std::string(50, '=') << std::endl;
    std::cout << "    Student Management System (Excel)" << std::endl;
    std::cout << std::string(50, '=') << std::endl;
    std::cout << "1. Add new student" << std::endl;
    std::cout << "2. View all students" << std::endl;
    std::cout << "3. Create test files with random data" << std::endl;
    std::cout << "4. Load all student files" << std::endl;
    std::cout << "5. Show statistics" << std::endl;
    std::cout << "6. Delete all student files" << std::endl;
    std::cout << "7. Save and exit" << std::endl;
    std::cout << std::string(50, '-') << std::endl;
    std::cout << "Enter your choice (1-7): ";
}

// Main function
int main() {
    std::cout << "Welcome to Student Management System!" << std::endl;
    std::cout << "Initializing system..." << std::endl;
    
    int choice;
    
    while (true) {
        show_main_menu();
        
        if (!(std::cin >> choice)) {
            std::cout << "Error: Please enter a valid number (1-7).\n";
            clear_input_buffer();
            continue;
        }

        switch (choice) {
            case 1:
                add_student();
                break;
            case 2:
                view_all_students();
                break;
            case 3:
                create_test_files();
                break;
            case 4:
                load_all_student_files();
                break;
            case 5:
                show_statistics();
                break;
            case 6:
                delete_all_files();
                break;
            case 7:
                if (!students.empty()) {
                    save_students_to_excel("students.xlsx");
                }
                std::cout << "Thank you for using Student Management System!\n";
                std::cout << "Goodbye!\n";
                return 0;
            default:
                std::cout << "Invalid choice! Please enter a number between 1 and 7.\n";
        }
        
        // Pause before showing menu again
        std::cout << "\nPress Enter to continue...";
        std::cin.ignore();
        std::cin.get();
    }
    
    return 0;
}
