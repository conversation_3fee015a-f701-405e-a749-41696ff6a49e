===============================================================================
                    تعليمات ChatGPT لإنشاء برنامج إدارة البيانات
                   ChatGPT Instructions for Data Management System
===============================================================================

🎯 الهدف:
إنشاء برنامج إدارة بيانات شامل باستخدام C++17 مع دعم Excel والإحصائيات
بناءً على القالب المرفق، مع تخصيصه لنوع البيانات المطلوب.

===============================================================================
📋 التعليمات الأساسية لـ ChatGPT:
===============================================================================

أريد منك إنشاء برنامج إدارة بيانات باستخدام C++17 بناءً على القالب المرفق.
يرجى اتباع هذه المواصفات بدقة:

🎯 **نوع البيانات المطلوب:**
[يرجى تحديد نوع البيانات هنا - مثال: إدارة المكتبة، إدارة الموظفين، إدارة المخزون]

📊 **تفاصيل البيانات:**
- **اسم الكيان:** [مثال: Book, Employee, Product]
- **الحقل الأول (نصي):** [مثال: العنوان، الاسم، اسم المنتج]
- **الحقل الثاني (رقمي):** [مثال: عدد الصفحات، الراتب، السعر]
- **الحقل الثالث (تصنيف):** [مثال: النوع، القسم، الفئة]
- **النطاق الرقمي:** من [الحد الأدنى] إلى [الحد الأعلى]

🎲 **البيانات العشوائية المطلوبة:**
- **قائمة الخيارات للحقل الأول:** [20-40 خيار واقعي]
- **قائمة التصنيفات:** [10-20 تصنيف مناسب]
- **عدد السجلات لكل ملف:** من [5] إلى [15] سجل

🎨 **متطلبات واجهة المستخدم:**
- قائمة رئيسية بـ 7 خيارات
- رسائل واضحة باللغة [العربية/الإنجليزية]
- عرض التقدم أثناء العمليات الطويلة
- تنسيق احترافي للجداول والإحصائيات

===============================================================================
🔧 المتطلبات التقنية:
===============================================================================

✅ **معايير البرمجة:**
- استخدام C++17 أو أحدث
- اتباع أفضل الممارسات في البرمجة
- معالجة شاملة للأخطاء
- تعليقات واضحة ومفيدة
- أسماء متغيرات ووظائف معبرة

✅ **الوظائف المطلوبة:**
1. إضافة سجل جديد يدوياً
2. عرض جميع السجلات
3. إنشاء ملفات اختبار بعدد مخصص (1-1000)
4. تحميل جميع الملفات
5. عرض إحصائيات شاملة
6. حذف جميع الملفات
7. حفظ والخروج

✅ **ميزات الإحصائيات:**
- إحصائيات الحقل الرقمي (متوسط، أدنى، أعلى، توزيع)
- إحصائيات التصنيفات مع النسب المئوية
- أكثر 5 تصنيفات شعبية
- أكثر القيم شيوعاً في الحقل الأول

✅ **دعم Excel:**
- إنشاء ملفات Excel بتنسيق احترافي
- رؤوس منسقة (عريض، وسط، خلفية رمادية)
- حفظ البيانات بشكل منظم
- أسماء ملفات متسلسلة

===============================================================================
📝 تعليمات التنفيذ المفصلة:
===============================================================================

🏗️ **الخطوة 1: إعداد الهيكل الأساسي**
```
يرجى إنشاء الملفات التالية:
1. [entity_name]_manager.cpp (الملف الرئيسي)
2. xlsxwriter.h (ملف رأس مكتبة Excel)
3. xlsxwriter.cpp (تنفيذ مكتبة Excel)
4. Makefile أو build.bat (نظام البناء)
```

🎯 **الخطوة 2: تخصيص القالب**
```
استبدل المتغيرات التالية في القالب:
- [ENTITY_NAME] → [اسم الكيان المحدد]
- [FIELD_1] → [اسم الحقل الأول]
- [FIELD_2] → [اسم الحقل الرقمي]
- [FIELD_3] → [اسم حقل التصنيف]
- [SYSTEM_NAME] → [اسم النظام]
```

📊 **الخطوة 3: إعداد البيانات العشوائية**
```
أنشئ قوائم واقعية للبيانات العشوائية:
- قائمة بـ 20-40 خيار للحقل الأول
- قائمة بـ 10-20 تصنيف مناسب
- تأكد من أن البيانات واقعية ومناسبة للمجال
```

🎨 **الخطوة 4: تخصيص واجهة المستخدم**
```
اجعل الواجهة سهلة الاستخدام:
- رسائل ترحيب واضحة
- قوائم منسقة بشكل احترافي
- رسائل خطأ مفيدة
- تأكيدات للعمليات الحساسة
```

===============================================================================
🧪 متطلبات الاختبار:
===============================================================================

✅ **اختبارات أساسية:**
- تجميع البرنامج بدون أخطاء
- إضافة سجل جديد وعرضه
- إنشاء 10 ملفات اختبار
- تحميل الملفات وعرض الإحصائيات
- حذف الملفات

✅ **اختبارات متقدمة:**
- إنشاء 100+ ملف (اختبار الأداء)
- إدخال بيانات غير صحيحة (اختبار معالجة الأخطاء)
- ملء الذاكرة بـ 1000+ سجل
- اختبار جميع خيارات القائمة

===============================================================================
📋 قائمة التسليم المطلوبة:
===============================================================================

📁 **الملفات المطلوبة:**
1. **الكود المصدري الكامل** (جميع ملفات .cpp و .h)
2. **ملف البناء** (Makefile أو .bat)
3. **ملف README** يشرح كيفية التجميع والاستخدام
4. **ملف أمثلة** يوضح البيانات العشوائية المستخدمة
5. **تقرير اختبار** يوضح نتائج الاختبارات

📊 **المعلومات المطلوبة:**
- عدد أسطر الكود
- الوقت المتوقع للتجميع
- استهلاك الذاكرة المتوقع
- أمثلة على الإحصائيات المولدة

===============================================================================
🎯 أمثلة محددة للتخصيص:
===============================================================================

📚 **مثال 1: نظام إدارة المكتبة**
```
نوع البيانات: إدارة الكتب
الكيان: Book
الحقول:
- العنوان (نصي): أسماء كتب حقيقية
- عدد الصفحات (رقمي): 50-800 صفحة
- النوع (تصنيف): رواية، علمي، تاريخ، أدب، إلخ

البيانات العشوائية:
- 30 عنوان كتاب مشهور
- 15 نوع أدبي مختلف
```

🏢 **مثال 2: نظام إدارة الموظفين**
```
نوع البيانات: إدارة الموظفين
الكيان: Employee
الحقول:
- الاسم (نصي): أسماء عربية حقيقية
- الراتب (رقمي): 3000-15000
- القسم (تصنيف): IT، المالية، الموارد البشرية، إلخ

البيانات العشوائية:
- 40 اسم موظف (20 ذكور + 20 إناث)
- 12 قسم مختلف في الشركة
```

===============================================================================
⚠️ تنبيهات مهمة:
===============================================================================

🚨 **يرجى الانتباه إلى:**
- استخدام معالجة الأخطاء الشاملة
- التحقق من صحة الإدخال في جميع الحالات
- تحسين الأداء للأعداد الكبيرة
- توافق الكود مع أنظمة التشغيل المختلفة
- وضوح التعليقات والتوثيق

🔧 **متطلبات إضافية:**
- إضافة وظيفة البحث (اختيارية)
- دعم تصدير CSV (اختيارية)
- نظام النسخ الاحتياطية (اختيارية)
- واجهة ملونة للطرفية (اختيارية)

===============================================================================
📞 طلب المساعدة:
===============================================================================

إذا كنت بحاجة لتوضيحات إضافية، يرجى السؤال عن:
- تفاصيل أكثر حول نوع البيانات
- أمثلة محددة للبيانات العشوائية
- متطلبات خاصة للواجهة
- معايير أداء محددة
- ميزات إضافية مطلوبة

===============================================================================
🎉 النتيجة المتوقعة:
===============================================================================

برنامج C++ احترافي وشامل لإدارة البيانات مع:
✅ واجهة مستخدم سهلة وواضحة
✅ دعم كامل لملفات Excel
✅ بيانات عشوائية واقعية
✅ إحصائيات تفصيلية ومفيدة
✅ أداء محسن وموثوقية عالية
✅ كود منظم وقابل للصيانة
✅ توثيق شامل وواضح

===============================================================================
تاريخ الإنشاء: 2025-01-25
الإصدار: 1.0 Instructions
المطور: AI Assistant
===============================================================================
