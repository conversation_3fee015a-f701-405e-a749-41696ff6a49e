# Student Management System Makefile
# Compatible with Windows (MinGW), Linux, and macOS

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2
INCLUDES = -I.

# Target executable name
TARGET = student_manager
ifeq ($(OS),Windows_NT)
    TARGET = student_manager.exe
endif

# Source files
SOURCES = student_manager.cpp xlsxwriter.cpp
HEADERS = xlsxwriter.h

# Object files
OBJECTS = $(SOURCES:.cpp=.o)

# Default target
all: $(TARGET)

# Build the main executable
$(TARGET): $(OBJECTS)
	@echo "Linking $(TARGET)..."
	$(CXX) $(OBJECTS) -o $(TARGET)
	@echo "Build successful! Executable: $(TARGET)"

# Compile source files to object files
%.o: %.cpp $(HEADERS)
	@echo "Compiling $<..."
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
ifeq ($(OS),Windows_NT)
	del /Q *.o $(TARGET) 2>nul || true
else
	rm -f *.o $(TARGET)
endif
	@echo "Clean complete."

# Clean all generated files (including test files)
clean-all: clean
	@echo "Cleaning all generated files..."
ifeq ($(OS),Windows_NT)
	del /Q students*.xlsx 2>nul || true
else
	rm -f students*.xlsx
endif
	@echo "All files cleaned."

# Run the program
run: $(TARGET)
	@echo "Running $(TARGET)..."
	./$(TARGET)

# Install dependencies (placeholder)
install-deps:
	@echo "No external dependencies required."
	@echo "Make sure you have g++ with C++17 support installed."

# Test build (compile only, don't run)
test-build: $(TARGET)
	@echo "Test build successful!"

# Show help
help:
	@echo "Student Management System - Build Help"
	@echo "======================================"
	@echo ""
	@echo "Available targets:"
	@echo "  all         - Build the program (default)"
	@echo "  clean       - Remove build artifacts"
	@echo "  clean-all   - Remove all generated files"
	@echo "  run         - Build and run the program"
	@echo "  test-build  - Test compilation only"
	@echo "  help        - Show this help message"
	@echo ""
	@echo "Usage examples:"
	@echo "  make              # Build the program"
	@echo "  make clean        # Clean build files"
	@echo "  make run          # Build and run"
	@echo "  make clean-all    # Remove everything"

# Phony targets
.PHONY: all clean clean-all run install-deps test-build help
