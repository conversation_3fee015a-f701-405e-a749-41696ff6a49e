#!/bin/bash

echo "========================================"
echo "  Student Management System - Build"
echo "========================================"
echo

echo "Checking for C++ compiler..."
if ! command -v g++ &> /dev/null; then
    echo "Error: g++ compiler not found!"
    echo "Please install g++ using your package manager:"
    echo "  Ubuntu/Debian: sudo apt install g++"
    echo "  CentOS/RHEL:   sudo yum install gcc-c++"
    echo "  macOS:         xcode-select --install"
    exit 1
fi

echo "Compiler found. Building project..."
echo

echo "Compiling student_manager.cpp..."
g++ -std=c++17 -Wall -Wextra -O2 -o student_manager student_manager.cpp xlsxwriter.cpp -I.

if [ $? -eq 0 ]; then
    echo
    echo "========================================"
    echo "  Build Successful!"
    echo "========================================"
    echo
    echo "Executable created: student_manager"
    echo
    echo "To run the program, type:"
    echo "  ./student_manager"
    echo
    chmod +x student_manager
else
    echo
    echo "========================================"
    echo "  Build Failed!"
    echo "========================================"
    echo
    echo "Please check the error messages above."
    echo "Make sure all source files are present:"
    echo "  - student_manager.cpp"
    echo "  - xlsxwriter.cpp"
    echo "  - xlsxwriter.h"
    echo
    exit 1
fi
