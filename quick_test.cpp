#include <iostream>
#include <random>
#include <vector>
#include <string>

int main() {
    std::cout << "Testing random data generation...\n";
    
    std::vector<std::string> first_names = {
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>"
    };
    
    std::vector<std::string> departments = {
        "Computer Science", "Engineering", "Medicine", "Business"
    };
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> name_dist(0, first_names.size() - 1);
    std::uniform_int_distribution<> dept_dist(0, departments.size() - 1);
    std::uniform_int_distribution<> age_dist(18, 25);
    
    std::cout << "Sample random students:\n";
    for (int i = 0; i < 5; ++i) {
        std::cout << "Student " << (i+1) << ": " 
                  << first_names[name_dist(gen)] << ", Age: " 
                  << age_dist(gen) << ", Dept: " 
                  << departments[dept_dist(gen)] << "\n";
    }
    
    std::cout << "Random generation test completed successfully!\n";
    return 0;
}
