@echo off
echo ========================================
echo   Student Management System - Tests
echo ========================================
echo.

echo Step 1: Testing xlsxwriter library...
echo Compiling test program...
g++ -std=c++17 -Wall -o test_xlsxwriter.exe test_xlsxwriter.cpp xlsxwriter.cpp -I.

if %errorlevel% neq 0 (
    echo ERROR: Failed to compile test program!
    echo Make sure g++ is installed and available in PATH.
    pause
    exit /b 1
)

echo Running xlsxwriter tests...
test_xlsxwriter.exe

if %errorlevel% neq 0 (
    echo ERROR: xlsxwriter tests failed!
    pause
    exit /b 1
)

echo.
echo Step 2: Testing main program compilation...
echo Compiling student management system...
g++ -std=c++17 -Wall -o student_manager.exe student_manager.cpp xlsxwriter.cpp -I.

if %errorlevel% neq 0 (
    echo ERROR: Failed to compile main program!
    pause
    exit /b 1
)

echo.
echo ========================================
echo   All Tests Passed!
echo ========================================
echo.
echo Programs compiled successfully:
echo   - test_xlsxwriter.exe
echo   - student_manager.exe
echo.
echo Test output file created:
echo   - test.xlsx
echo.
echo To run the student management system:
echo   student_manager.exe
echo.
echo To clean up test files:
echo   del test_xlsxwriter.exe test.xlsx
echo.

pause
