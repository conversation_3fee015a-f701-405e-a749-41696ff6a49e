===============================================================================
                    تعليمات ChatGPT لإنشاء ملف JSON للبرنامج
                   ChatGPT Instructions for JSON Configuration File
===============================================================================

🎯 الهدف:
إنشاء ملف JSON شامل يحتوي على جميع إعدادات وبيانات البرنامج المطلوب
لتسهيل التخصيص والتطوير المستقبلي.

===============================================================================
📋 التعليمات الأساسية لـ ChatGPT:
===============================================================================

أريد منك إنشاء ملف JSON شامل للبرنامج الذي تم إنشاؤه، يحتوي على جميع
الإعدادات والبيانات والمعلومات اللازمة. يرجى اتباع هذه المواصفات:

🎯 **نوع البرنامج:**
[يرجى تحديد نوع البرنامج هنا - مثال: إدارة المكتبة، إدارة الموظفين، إدارة المخزون]

📊 **متطلبات ملف JSON:**
- هيكل منظم وواضح
- جميع البيانات العشوائية
- إعدادات النظام
- معلومات الحقول والتحقق
- قوائم الخيارات والتصنيفات
- إعدادات واجهة المستخدم
- معلومات التوثيق

===============================================================================
🏗️ هيكل ملف JSON المطلوب:
===============================================================================

```json
{
  "project_info": {
    "name": "[اسم المشروع]",
    "version": "1.0.0",
    "description": "[وصف المشروع]",
    "author": "AI Assistant",
    "creation_date": "2025-01-25",
    "language": "C++17",
    "type": "[نوع النظام]"
  },
  
  "system_config": {
    "entity_name": "[اسم الكيان]",
    "entity_plural": "[جمع الكيان]",
    "system_title": "[عنوان النظام]",
    "default_filename": "[اسم الملف الافتراضي]",
    "excel_sheet_name": "[اسم ورقة Excel]",
    "max_files_limit": 1000,
    "min_files_limit": 1,
    "records_per_file": {
      "min": 5,
      "max": 15
    }
  },
  
  "data_fields": {
    "field_1": {
      "name": "[اسم الحقل الأول]",
      "type": "string",
      "display_name": "[اسم العرض]",
      "max_length": 50,
      "required": true,
      "validation": "non_empty"
    },
    "field_2": {
      "name": "[اسم الحقل الثاني]",
      "type": "integer",
      "display_name": "[اسم العرض]",
      "min_value": [الحد الأدنى],
      "max_value": [الحد الأعلى],
      "required": true,
      "validation": "range"
    },
    "field_3": {
      "name": "[اسم الحقل الثالث]",
      "type": "string",
      "display_name": "[اسم العرض]",
      "max_length": 30,
      "required": true,
      "validation": "non_empty"
    }
  },
  
  "random_data": {
    "field_1_options": [
      "[خيار 1]",
      "[خيار 2]",
      "[خيار 3]",
      // ... 20-40 خيار
    ],
    "field_3_categories": [
      "[تصنيف 1]",
      "[تصنيف 2]",
      "[تصنيف 3]",
      // ... 10-20 تصنيف
    ]
  },
  
  "ui_config": {
    "language": "arabic",
    "menu_options": [
      {
        "id": 1,
        "text_ar": "إضافة [كيان] جديد",
        "text_en": "Add new [entity]",
        "function": "add_entity"
      },
      {
        "id": 2,
        "text_ar": "عرض جميع [الكيانات]",
        "text_en": "View all [entities]",
        "function": "view_all"
      },
      // ... باقي خيارات القائمة
    ],
    "messages": {
      "welcome": "[رسالة الترحيب]",
      "goodbye": "[رسالة الوداع]",
      "invalid_input": "[رسالة خطأ الإدخال]",
      "success": "[رسالة النجاح]",
      "error": "[رسالة الخطأ]"
    }
  },
  
  "excel_config": {
    "headers": {
      "id": "ID",
      "field_1": "[عنوان الحقل الأول]",
      "field_2": "[عنوان الحقل الثاني]",
      "field_3": "[عنوان الحقل الثالث]"
    },
    "formatting": {
      "header_bold": true,
      "header_center": true,
      "header_background": "gray",
      "column_widths": [5, 20, 10, 25]
    }
  },
  
  "statistics_config": {
    "enabled_stats": [
      "count_total",
      "field_2_average",
      "field_2_min_max",
      "field_2_distribution",
      "field_3_distribution",
      "field_3_top_5",
      "field_1_frequency"
    ],
    "display_format": {
      "decimal_places": 1,
      "percentage_format": true,
      "table_width": 50
    }
  },
  
  "performance_config": {
    "progress_update_interval": 10,
    "max_memory_usage_mb": 500,
    "file_processing_batch_size": 100,
    "statistics_cache_enabled": true
  },
  
  "file_patterns": {
    "input_pattern": "[entities]_*.xlsx",
    "output_pattern": "[entities]_{number}.xlsx",
    "backup_pattern": "[entities]_backup_{timestamp}.xlsx",
    "final_output": "[entities]_final.xlsx"
  },
  
  "validation_rules": {
    "field_1": {
      "min_length": 2,
      "max_length": 50,
      "allowed_chars": "letters_spaces_arabic",
      "forbidden_words": []
    },
    "field_2": {
      "data_type": "integer",
      "min_value": "[الحد الأدنى]",
      "max_value": "[الحد الأعلى]"
    },
    "field_3": {
      "min_length": 2,
      "max_length": 30,
      "predefined_list": false
    }
  },
  
  "build_config": {
    "compiler": "g++",
    "standard": "c++17",
    "source_files": [
      "[entity]_manager.cpp",
      "xlsxwriter.cpp"
    ],
    "header_files": [
      "xlsxwriter.h"
    ],
    "output_executable": "[entity]_manager",
    "compile_flags": [
      "-std=c++17",
      "-O2",
      "-Wall"
    ]
  },
  
  "testing_config": {
    "test_scenarios": [
      {
        "name": "basic_functionality",
        "description": "اختبار الوظائف الأساسية",
        "steps": [
          "إضافة سجل جديد",
          "عرض السجلات",
          "حفظ في Excel"
        ]
      },
      {
        "name": "bulk_operations",
        "description": "اختبار العمليات المجمعة",
        "steps": [
          "إنشاء 10 ملفات",
          "تحميل الملفات",
          "عرض الإحصائيات"
        ]
      },
      {
        "name": "stress_test",
        "description": "اختبار الضغط",
        "steps": [
          "إنشاء 100+ ملف",
          "تحميل 1000+ سجل",
          "قياس الأداء"
        ]
      }
    ],
    "expected_performance": {
      "file_creation_per_second": 3,
      "records_loading_per_second": 200,
      "memory_usage_per_1000_records": "50MB"
    }
  },
  
  "documentation": {
    "readme_sections": [
      "المقدمة",
      "المتطلبات",
      "التثبيت",
      "الاستخدام",
      "الأمثلة",
      "استكشاف الأخطاء"
    ],
    "code_comments_language": "arabic",
    "user_manual_language": "arabic"
  },
  
  "future_enhancements": [
    {
      "feature": "قاعدة بيانات حقيقية",
      "priority": "high",
      "estimated_effort": "2 weeks"
    },
    {
      "feature": "واجهة رسومية",
      "priority": "medium",
      "estimated_effort": "3 weeks"
    },
    {
      "feature": "تصدير PDF",
      "priority": "low",
      "estimated_effort": "1 week"
    }
  ],
  
  "dependencies": {
    "required": [
      {
        "name": "C++ Compiler",
        "version": "C++17 or later",
        "purpose": "تجميع البرنامج"
      }
    ],
    "optional": [
      {
        "name": "Make",
        "version": "any",
        "purpose": "نظام البناء"
      }
    ]
  },
  
  "localization": {
    "supported_languages": ["arabic", "english"],
    "default_language": "arabic",
    "rtl_support": true,
    "date_format": "dd/mm/yyyy",
    "number_format": "arabic"
  }
}
```

===============================================================================
📝 تعليمات التخصيص:
===============================================================================

🎯 **الخطوة 1: تحديد المعلومات الأساسية**
```
يرجى ملء المعلومات التالية:
- اسم المشروع: [مثال: نظام إدارة المكتبة]
- نوع النظام: [مثال: Library Management System]
- اسم الكيان: [مثال: Book]
- جمع الكيان: [مثال: Books]
```

🔧 **الخطوة 2: تخصيص الحقول**
```
لكل حقل، حدد:
- الاسم التقني (field_1, field_2, field_3)
- اسم العرض (العنوان، العمر، القسم)
- نوع البيانات (string, integer)
- قواعد التحقق (النطاق، الطول، إلخ)
```

📊 **الخطوة 3: إعداد البيانات العشوائية**
```
أنشئ قوائم شاملة:
- 20-40 خيار للحقل الأول (أسماء، عناوين، إلخ)
- 10-20 تصنيف للحقل الثالث (أقسام، فئات، إلخ)
- تأكد من أن البيانات واقعية ومناسبة
```

🎨 **الخطوة 4: تخصيص واجهة المستخدم**
```
حدد:
- لغة الواجهة (عربي/إنجليزي)
- نصوص القوائم والرسائل
- تنسيق العرض والجداول
- رسائل الخطأ والنجاح
```

===============================================================================
🧪 متطلبات التحقق:
===============================================================================

✅ **تحقق من اكتمال الأقسام:**
□ معلومات المشروع الأساسية
□ إعدادات النظام والحقول
□ البيانات العشوائية الكاملة
□ إعدادات واجهة المستخدم
□ تكوين Excel والإحصائيات
□ قواعد التحقق والتصديق
□ إعدادات البناء والاختبار

✅ **تحقق من صحة البيانات:**
□ جميع القيم الرقمية منطقية
□ النصوص باللغة المناسبة
□ قوائم البيانات العشوائية مكتملة
□ إعدادات التحقق متسقة
□ مسارات الملفات صحيحة

===============================================================================
🎯 أمثلة محددة للتخصيص:
===============================================================================

📚 **مثال 1: نظام إدارة المكتبة**
```json
{
  "project_info": {
    "name": "نظام إدارة المكتبة",
    "type": "Library Management System"
  },
  "system_config": {
    "entity_name": "Book",
    "entity_plural": "Books"
  },
  "data_fields": {
    "field_1": {
      "name": "title",
      "display_name": "العنوان"
    },
    "field_2": {
      "name": "pages",
      "display_name": "عدد الصفحات",
      "min_value": 50,
      "max_value": 800
    },
    "field_3": {
      "name": "category",
      "display_name": "النوع"
    }
  },
  "random_data": {
    "field_1_options": [
      "الأسود يليق بك",
      "مئة عام من العزلة",
      "البؤساء",
      // ... المزيد من عناوين الكتب
    ],
    "field_3_categories": [
      "رواية",
      "علمي",
      "تاريخ",
      "أدب",
      "فلسفة"
      // ... المزيد من الأنواع
    ]
  }
}
```

🏢 **مثال 2: نظام إدارة الموظفين**
```json
{
  "project_info": {
    "name": "نظام إدارة الموظفين",
    "type": "Employee Management System"
  },
  "system_config": {
    "entity_name": "Employee",
    "entity_plural": "Employees"
  },
  "data_fields": {
    "field_1": {
      "name": "name",
      "display_name": "الاسم"
    },
    "field_2": {
      "name": "salary",
      "display_name": "الراتب",
      "min_value": 3000,
      "max_value": 15000
    },
    "field_3": {
      "name": "department",
      "display_name": "القسم"
    }
  },
  "random_data": {
    "field_1_options": [
      "أحمد محمد علي",
      "فاطمة حسن إبراهيم",
      "محمد عبد الله أحمد",
      // ... المزيد من أسماء الموظفين
    ],
    "field_3_categories": [
      "تقنية المعلومات",
      "الموارد البشرية",
      "المالية",
      "التسويق",
      "المبيعات"
      // ... المزيد من الأقسام
    ]
  }
}
```

===============================================================================
⚠️ تنبيهات مهمة:
===============================================================================

🚨 **يرجى الانتباه إلى:**
- استخدام تنسيق JSON صحيح (فواصل، أقواس، إلخ)
- التأكد من عدم وجود فواصل زائدة
- استخدام ترميز UTF-8 للنصوص العربية
- التحقق من صحة جميع القيم الرقمية
- مراجعة اكتمال جميع الأقسام

🔧 **متطلبات إضافية:**
- إضافة تعليقات توضيحية حيث أمكن
- استخدام أسماء واضحة ومعبرة
- تنظيم الهيكل بشكل منطقي
- التأكد من قابلية القراءة والفهم

===============================================================================
📋 قائمة التسليم المطلوبة:
===============================================================================

📁 **الملفات المطلوبة:**
1. **ملف JSON الرئيسي** ([entity]_config.json)
2. **ملف JSON للبيانات العشوائية** ([entity]_data.json) - اختياري
3. **ملف JSON للإعدادات** ([entity]_settings.json) - اختياري
4. **ملف README** يشرح كيفية استخدام ملفات JSON

📊 **معلومات إضافية:**
- حجم ملف JSON المتوقع
- عدد العناصر في كل قسم
- مثال على كيفية قراءة JSON في C++
- أدوات التحقق من صحة JSON

===============================================================================
🎉 النتيجة المتوقعة:
===============================================================================

ملف JSON شامل ومنظم يحتوي على:
✅ جميع إعدادات النظام
✅ البيانات العشوائية الكاملة
✅ تكوين واجهة المستخدم
✅ قواعد التحقق والتصديق
✅ إعدادات الأداء والاختبار
✅ معلومات التوثيق والبناء
✅ خطط التطوير المستقبلية

===============================================================================
تاريخ الإنشاء: 2025-01-25
الإصدار: 1.0 JSON Instructions
المطور: AI Assistant
===============================================================================
